#!/bin/bash

# Comprehensive development tools setup for enhanced AI assistance
# This script installs vector databases, additional MCP servers, and useful development tools

echo "🛠️ Setting up Advanced Development Tools for AI Enhancement"
echo "==========================================================="

# Check prerequisites
echo "🔍 Checking prerequisites..."

# Check Node.js
if ! command -v node &> /dev/null; then
    echo "❌ Node.js is required but not installed."
    exit 1
fi
echo "✅ Node.js: $(node --version)"

# Check Python
if ! command -v python &> /dev/null && ! command -v python3 &> /dev/null; then
    echo "❌ Python is required but not installed."
    exit 1
fi

PYTHON_CMD="python3"
if command -v python &> /dev/null; then
    PYTHON_CMD="python"
fi
echo "✅ Python: $($PYTHON_CMD --version)"

# Check if we're in the right directory
if [ ! -f "manage.py" ]; then
    echo "❌ Please run this script from your Django project root directory"
    exit 1
fi

echo "✅ Django project detected"

# Install additional MCP servers
echo ""
echo "📦 Installing Additional MCP Servers..."

additional_mcp_servers=(
    "@modelcontextprotocol/server-sqlite"
    "@modelcontextprotocol/server-puppeteer"
    "@modelcontextprotocol/server-fetch"
    "@modelcontextprotocol/server-everything"
    "@modelcontextprotocol/server-slack"
    "@modelcontextprotocol/server-github"
)

for server in "${additional_mcp_servers[@]}"; do
    echo "   Installing $server..."
    timeout 30 npm install -g "$server" > /dev/null 2>&1 || echo "     (Will install on first use)"
done

# Set up vector database (Chroma)
echo ""
echo "🗄️ Setting up Vector Database (ChromaDB)..."

$PYTHON_CMD -m pip install --upgrade pip > /dev/null 2>&1
$PYTHON_CMD -m pip install chromadb sentence-transformers > /dev/null 2>&1

if [ $? -eq 0 ]; then
    echo "✅ ChromaDB installed successfully"
    
    # Create a simple vector database setup script
    cat > setup_vector_db.py << 'EOF'
#!/usr/bin/env python3
"""
Set up ChromaDB vector database for the Django project.
This creates embeddings for your codebase to enhance AI assistance.
"""

import os
import chromadb
from chromadb.config import Settings
import glob
from sentence_transformers import SentenceTransformer

def setup_chroma_db():
    """Set up ChromaDB with project code embeddings."""
    
    # Initialize ChromaDB
    client = chromadb.PersistentClient(path="./chroma_db")
    
    # Create or get collection
    collection = client.get_or_create_collection(
        name="django_project_code",
        metadata={"description": "Django driving school project code embeddings"}
    )
    
    # Initialize sentence transformer
    model = SentenceTransformer('all-MiniLM-L6-v2')
    
    # Find Python files
    python_files = []
    for pattern in ['**/*.py', '**/*.md', '**/*.txt']:
        python_files.extend(glob.glob(pattern, recursive=True))
    
    # Filter out unwanted files
    excluded_patterns = [
        '__pycache__', '.git', 'node_modules', 'venv', 'env',
        'staticfiles', 'media', '.pytest_cache', 'chroma_db'
    ]
    
    filtered_files = []
    for file in python_files:
        if not any(pattern in file for pattern in excluded_patterns):
            filtered_files.append(file)
    
    print(f"Processing {len(filtered_files)} files...")
    
    # Process files in batches
    batch_size = 10
    for i in range(0, len(filtered_files), batch_size):
        batch_files = filtered_files[i:i+batch_size]
        documents = []
        metadatas = []
        ids = []
        
        for file_path in batch_files:
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                    if content.strip():  # Only process non-empty files
                        documents.append(content[:8000])  # Limit content length
                        metadatas.append({
                            'file_path': file_path,
                            'file_type': os.path.splitext(file_path)[1],
                            'size': len(content)
                        })
                        ids.append(f"file_{i}_{file_path.replace('/', '_').replace('\\', '_')}")
            except Exception as e:
                print(f"Error processing {file_path}: {e}")
        
        if documents:
            try:
                collection.add(
                    documents=documents,
                    metadatas=metadatas,
                    ids=ids
                )
                print(f"Added batch {i//batch_size + 1}/{(len(filtered_files) + batch_size - 1)//batch_size}")
            except Exception as e:
                print(f"Error adding batch: {e}")
    
    print("✅ Vector database setup complete!")
    print(f"📊 Processed {len(filtered_files)} files")
    print("🔍 You can now use vector search for enhanced code assistance")

if __name__ == "__main__":
    setup_chroma_db()
EOF
    
    echo "   Running vector database setup..."
    $PYTHON_CMD setup_vector_db.py
    
else
    echo "⚠️ ChromaDB installation failed, skipping vector database setup"
fi

# Set up Redis for caching (if not already installed)
echo ""
echo "🔄 Setting up Redis for Enhanced Caching..."

if command -v redis-server &> /dev/null; then
    echo "✅ Redis is already installed"
else
    echo "📥 Installing Redis..."
    
    # Try different package managers
    if command -v apt-get &> /dev/null; then
        sudo apt-get update && sudo apt-get install -y redis-server
    elif command -v yum &> /dev/null; then
        sudo yum install -y redis
    elif command -v brew &> /dev/null; then
        brew install redis
    elif command -v choco &> /dev/null; then
        choco install redis-64
    else
        echo "⚠️ Could not install Redis automatically. Please install manually."
        echo "   Windows: Download from https://github.com/microsoftarchive/redis/releases"
        echo "   macOS: brew install redis"
        echo "   Ubuntu: sudo apt-get install redis-server"
    fi
fi

# Install Python Redis client
$PYTHON_CMD -m pip install redis django-redis > /dev/null 2>&1

# Set up Elasticsearch (optional, for advanced search)
echo ""
echo "🔍 Setting up Elasticsearch (Optional)..."

if command -v docker &> /dev/null; then
    echo "   Setting up Elasticsearch with Docker..."
    
    # Create docker-compose for Elasticsearch
    cat > docker-compose.elasticsearch.yml << 'EOF'
version: '3.8'
services:
  elasticsearch:
    image: docker.elastic.co/elasticsearch/elasticsearch:8.11.0
    container_name: jw-elasticsearch
    environment:
      - discovery.type=single-node
      - xpack.security.enabled=false
      - "ES_JAVA_OPTS=-Xms512m -Xmx512m"
    ports:
      - "9200:9200"
      - "9300:9300"
    volumes:
      - elasticsearch_data:/usr/share/elasticsearch/data
    restart: unless-stopped

volumes:
  elasticsearch_data:
EOF
    
    echo "   ✅ Elasticsearch Docker Compose created"
    echo "   To start: docker-compose -f docker-compose.elasticsearch.yml up -d"
    
    # Install Python Elasticsearch client
    $PYTHON_CMD -m pip install elasticsearch django-elasticsearch-dsl > /dev/null 2>&1
    
else
    echo "   ⚠️ Docker not found. Skipping Elasticsearch setup."
fi

# Create enhanced MCP configuration with all services
echo ""
echo "🔧 Creating Enhanced MCP Configuration..."

cat > vscode-mcp-enhanced.json << EOF
{
  "chat.agent.enabled": true,
  "mcp.servers": {
    "context7": {
      "command": "npx",
      "args": ["-y", "@upstash/context7-mcp@latest"],
      "env": {},
      "description": "Up-to-date documentation for libraries and frameworks"
    },
    "filesystem": {
      "command": "npx",
      "args": ["-y", "@modelcontextprotocol/server-filesystem", "$(pwd)"],
      "env": {},
      "description": "File system operations for the project"
    },
    "git": {
      "command": "npx",
      "args": ["-y", "@modelcontextprotocol/server-git"],
      "env": {},
      "description": "Git operations and repository information"
    },
    "postgres": {
      "command": "npx",
      "args": ["-y", "@modelcontextprotocol/server-postgres"],
      "env": {
        "POSTGRES_CONNECTION_STRING": "**********************************************/JW"
      },
      "description": "PostgreSQL database queries and schema information"
    },
    "sqlite": {
      "command": "npx",
      "args": ["-y", "@modelcontextprotocol/server-sqlite"],
      "env": {},
      "description": "SQLite database operations for local development"
    },
    "puppeteer": {
      "command": "npx",
      "args": ["-y", "@modelcontextprotocol/server-puppeteer"],
      "env": {},
      "description": "Web scraping and browser automation"
    },
    "fetch": {
      "command": "npx",
      "args": ["-y", "@modelcontextprotocol/server-fetch"],
      "env": {},
      "description": "HTTP requests and API interactions"
    },
    "sequential-thinking": {
      "command": "npx",
      "args": ["-y", "@modelcontextprotocol/server-sequential-thinking"],
      "env": {},
      "description": "Enhanced reasoning and step-by-step thinking"
    },
    "github": {
      "command": "npx",
      "args": ["-y", "@modelcontextprotocol/server-github"],
      "env": {
        "GITHUB_PERSONAL_ACCESS_TOKEN": "your_github_token_here"
      },
      "description": "GitHub repository operations (requires token)"
    }
  },
  "mcp.logging.level": "info",
  "mcp.timeout": 30000,
  "mcp.retries": 3,
  "mcp.experimental.vectorSearch": true
}
EOF

echo "✅ Enhanced MCP configuration created"

# Create development utilities
echo ""
echo "🛠️ Creating Development Utilities..."

# Create a code analysis script
cat > analyze_code.py << 'EOF'
#!/usr/bin/env python3
"""
Code analysis utility for the Django project.
Provides insights and suggestions for code improvement.
"""

import os
import ast
import glob
from collections import defaultdict, Counter

def analyze_django_project():
    """Analyze the Django project structure and code quality."""
    
    print("🔍 Analyzing Django Project...")
    
    # Find Python files
    python_files = glob.glob('**/*.py', recursive=True)
    python_files = [f for f in python_files if '__pycache__' not in f and 'venv' not in f]
    
    stats = {
        'total_files': len(python_files),
        'total_lines': 0,
        'views': 0,
        'models': 0,
        'forms': 0,
        'functions': 0,
        'classes': 0,
        'imports': defaultdict(int)
    }
    
    for file_path in python_files:
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
                lines = content.split('\n')
                stats['total_lines'] += len(lines)
                
                # Parse AST
                try:
                    tree = ast.parse(content)
                    
                    for node in ast.walk(tree):
                        if isinstance(node, ast.FunctionDef):
                            stats['functions'] += 1
                            if 'views.py' in file_path:
                                stats['views'] += 1
                        elif isinstance(node, ast.ClassDef):
                            stats['classes'] += 1
                            if 'models.py' in file_path:
                                stats['models'] += 1
                            elif 'forms.py' in file_path:
                                stats['forms'] += 1
                        elif isinstance(node, ast.Import):
                            for alias in node.names:
                                stats['imports'][alias.name] += 1
                        elif isinstance(node, ast.ImportFrom):
                            if node.module:
                                stats['imports'][node.module] += 1
                
                except SyntaxError:
                    pass
                    
        except Exception as e:
            print(f"Error analyzing {file_path}: {e}")
    
    # Print results
    print(f"\n📊 Project Statistics:")
    print(f"   Total Python files: {stats['total_files']}")
    print(f"   Total lines of code: {stats['total_lines']:,}")
    print(f"   Functions: {stats['functions']}")
    print(f"   Classes: {stats['classes']}")
    print(f"   Views: {stats['views']}")
    print(f"   Models: {stats['models']}")
    print(f"   Forms: {stats['forms']}")
    
    print(f"\n📚 Most Used Imports:")
    for module, count in Counter(stats['imports']).most_common(10):
        print(f"   {module}: {count}")

if __name__ == "__main__":
    analyze_django_project()
EOF

echo "✅ Code analysis utility created"

# Summary
echo ""
echo "📊 Advanced Development Tools Setup Complete!"
echo "=============================================="

echo ""
echo "✅ Installed Tools:"
echo "   - Additional MCP servers (SQLite, Puppeteer, Fetch, etc.)"
echo "   - ChromaDB vector database for code embeddings"
echo "   - Redis for enhanced caching"
echo "   - Elasticsearch setup (Docker-based)"
echo "   - Enhanced MCP configuration"
echo "   - Code analysis utilities"

echo ""
echo "🎯 Next Steps:"
echo "1. Restart VS Code to load the enhanced MCP configuration"
echo "2. Optional: Start Elasticsearch with 'docker-compose -f docker-compose.elasticsearch.yml up -d'"
echo "3. Optional: Start Redis service"
echo "4. Run 'python analyze_code.py' for project insights"
echo "5. Try advanced AI prompts with vector search capabilities"

echo ""
echo "🚀 Enhanced AI Capabilities:"
echo "   - Vector-based code search and similarity matching"
echo "   - Real-time database queries and schema analysis"
echo "   - Web scraping and API interactions"
echo "   - GitHub integration (with token)"
echo "   - Advanced caching and search capabilities"

echo ""
echo "🎉 Your development environment is now supercharged for AI assistance!"
