# PowerShell script to set up MCP services for VS Code (both stable and insiders)
# Run this script as: powershell -ExecutionPolicy Bypass -File setup-vscode-mcp.ps1

Write-Host "🚀 Setting up MCP Services for VS Code" -ForegroundColor Green
Write-Host "=======================================" -ForegroundColor Green

# Check if Node.js is installed
if (-not (Get-Command node -ErrorAction SilentlyContinue)) {
    Write-Host "❌ Node.js is not installed. Please install Node.js first." -ForegroundColor Red
    Write-Host "   Download from: https://nodejs.org/" -ForegroundColor Yellow
    exit 1
}

Write-Host "✅ Node.js is installed: $(node --version)" -ForegroundColor Green

# VS Code settings paths
$vscodeStablePath = "$env:APPDATA\Code\User\settings.json"
$vscodeInsidersPath = "$env:APPDATA\Code - Insiders\User\settings.json"

# Current project path
$currentPath = (Get-Location).Path
Write-Host "📁 Current project directory: $currentPath" -ForegroundColor Cyan

# Read the MCP configuration template
$mcpConfigPath = "vscode-mcp-setup.json"
if (-not (Test-Path $mcpConfigPath)) {
    Write-Host "❌ MCP configuration file not found: $mcpConfigPath" -ForegroundColor Red
    exit 1
}

$mcpConfig = Get-Content $mcpConfigPath -Raw | ConvertFrom-Json

# Update filesystem path to current directory
$mcpConfig.'mcp.servers'.filesystem.args[2] = $currentPath

# Function to update VS Code settings
function Update-VSCodeSettings {
    param(
        [string]$settingsPath,
        [string]$versionName
    )
    
    Write-Host "🔧 Configuring $versionName..." -ForegroundColor Yellow
    
    # Create directory if it doesn't exist
    $settingsDir = Split-Path $settingsPath -Parent
    if (-not (Test-Path $settingsDir)) {
        New-Item -ItemType Directory -Path $settingsDir -Force | Out-Null
        Write-Host "   Created settings directory: $settingsDir" -ForegroundColor Gray
    }
    
    # Read existing settings or create new
    $settings = @{}
    if (Test-Path $settingsPath) {
        try {
            $existingContent = Get-Content $settingsPath -Raw
            if ($existingContent.Trim()) {
                $settings = $existingContent | ConvertFrom-Json -AsHashtable
            }
        }
        catch {
            Write-Host "   ⚠️ Could not parse existing settings, creating backup..." -ForegroundColor Yellow
            Copy-Item $settingsPath "$settingsPath.backup.$(Get-Date -Format 'yyyyMMdd-HHmmss')"
            $settings = @{}
        }
    }
    
    # Merge MCP configuration
    $settings["chat.agent.enabled"] = $mcpConfig.'chat.agent.enabled'
    $settings["mcp.servers"] = $mcpConfig.'mcp.servers'
    $settings["mcp.logging.level"] = $mcpConfig.'mcp.logging.level'
    $settings["mcp.timeout"] = $mcpConfig.'mcp.timeout'
    $settings["mcp.retries"] = $mcpConfig.'mcp.retries'
    
    # Write updated settings
    try {
        $settings | ConvertTo-Json -Depth 10 | Set-Content $settingsPath -Encoding UTF8
        Write-Host "   ✅ $versionName configured successfully" -ForegroundColor Green
        return $true
    }
    catch {
        Write-Host "   ❌ Failed to update $versionName settings: $($_.Exception.Message)" -ForegroundColor Red
        return $false
    }
}

# Configure VS Code Stable
$stableConfigured = $false
if (Get-Command code -ErrorAction SilentlyContinue) {
    $stableConfigured = Update-VSCodeSettings $vscodeStablePath "VS Code Stable"
}
else {
    Write-Host "⚠️ VS Code Stable not found in PATH" -ForegroundColor Yellow
}

# Configure VS Code Insiders
$insidersConfigured = $false
if (Get-Command code-insiders -ErrorAction SilentlyContinue) {
    $insidersConfigured = Update-VSCodeSettings $vscodeInsidersPath "VS Code Insiders"
}
else {
    Write-Host "⚠️ VS Code Insiders not found in PATH" -ForegroundColor Yellow
}

# Install useful VS Code extensions
Write-Host "`n🔌 Installing useful VS Code extensions..." -ForegroundColor Yellow

$extensions = @(
    "ms-python.python",
    "ms-python.debugpy",
    "ms-python.pylint",
    "ms-python.black-formatter",
    "ms-vscode.vscode-json",
    "redhat.vscode-yaml",
    "ms-vscode.powershell",
    "GitHub.copilot",
    "GitHub.copilot-chat",
    "ms-vscode.vscode-typescript-next",
    "bradlc.vscode-tailwindcss",
    "ms-vscode.vscode-eslint"
)

foreach ($extension in $extensions) {
    Write-Host "   Installing $extension..." -ForegroundColor Gray
    
    # Install for stable if available
    if ($stableConfigured) {
        code --install-extension $extension --force 2>$null
    }
    
    # Install for insiders if available
    if ($insidersConfigured) {
        code-insiders --install-extension $extension --force 2>$null
    }
}

# Pre-install some MCP packages to avoid first-use delays
Write-Host "`n📦 Pre-installing key MCP packages..." -ForegroundColor Yellow

$keyPackages = @(
    "@upstash/context7-mcp@latest",
    "@modelcontextprotocol/server-filesystem",
    "@modelcontextprotocol/server-git",
    "@modelcontextprotocol/server-postgres"
)

foreach ($package in $keyPackages) {
    Write-Host "   Pre-installing $package..." -ForegroundColor Gray
    Start-Process -FilePath "npx" -ArgumentList "-y", $package, "--help" -WindowStyle Hidden -Wait -TimeoutSec 30 -ErrorAction SilentlyContinue
}

# Summary
Write-Host "`n📊 Setup Summary" -ForegroundColor Green
Write-Host "=================" -ForegroundColor Green

if ($stableConfigured) {
    Write-Host "✅ VS Code Stable: Configured with MCP services" -ForegroundColor Green
}
else {
    Write-Host "❌ VS Code Stable: Not configured" -ForegroundColor Red
}

if ($insidersConfigured) {
    Write-Host "✅ VS Code Insiders: Configured with MCP services" -ForegroundColor Green
}
else {
    Write-Host "❌ VS Code Insiders: Not configured" -ForegroundColor Red
}

Write-Host "`n🎯 Next Steps:" -ForegroundColor Cyan
Write-Host "1. Restart VS Code (both versions if you have them)" -ForegroundColor White
Write-Host "2. Open your project in VS Code" -ForegroundColor White
Write-Host "3. Open the Command Palette (Ctrl+Shift+P)" -ForegroundColor White
Write-Host "4. Look for 'Chat: ' commands to start using MCP-enhanced AI" -ForegroundColor White
Write-Host "5. Try: 'Create a Django view with error handling. use context7'" -ForegroundColor White

Write-Host "`n🔧 Configured MCP Services:" -ForegroundColor Cyan
Write-Host "- Context7: Up-to-date documentation" -ForegroundColor White
Write-Host "- Filesystem: Project file operations" -ForegroundColor White
Write-Host "- Git: Repository information" -ForegroundColor White
Write-Host "- PostgreSQL: Database queries" -ForegroundColor White
Write-Host "- Sequential Thinking: Enhanced reasoning" -ForegroundColor White
Write-Host "- And more..." -ForegroundColor White

Write-Host "`n🎉 MCP setup complete!" -ForegroundColor Green
