{"mcp": {"servers": {"context7": {"type": "stdio", "command": "npx", "args": ["-y", "@upstash/context7-mcp@latest"]}, "filesystem": {"type": "stdio", "command": "npx", "args": ["-y", "@modelcontextprotocol/server-filesystem", "d:\\mark_\\Documents\\JW"]}, "git": {"type": "stdio", "command": "npx", "args": ["-y", "@modelcontextprotocol/server-git"]}, "postgres": {"type": "stdio", "command": "npx", "args": ["-y", "@modelcontextprotocol/server-postgres"], "env": {"POSTGRES_CONNECTION_STRING": "**********************************************/JW"}}, "brave-search": {"type": "stdio", "command": "npx", "args": ["-y", "@modelcontextprotocol/server-brave-search"], "env": {"BRAVE_API_KEY": "your_brave_api_key_here"}}, "memory": {"type": "stdio", "command": "npx", "args": ["-y", "@modelcontextprotocol/server-memory"]}, "sequential-thinking": {"type": "stdio", "command": "npx", "args": ["-y", "@modelcontextprotocol/server-sequential-thinking"]}}}}