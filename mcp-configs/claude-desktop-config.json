{"mcpServers": {"Context7": {"command": "npx", "args": ["-y", "@upstash/context7-mcp@latest"]}, "Filesystem": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-filesystem", "d:\\mark_\\Documents\\JW"]}, "Git": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-git"]}, "PostgreSQL": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-postgres"], "env": {"POSTGRES_CONNECTION_STRING": "**********************************************/JW"}}, "BraveSearch": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-brave-search"], "env": {"BRAVE_API_KEY": "your_brave_api_key_here"}}, "Memory": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-memory"]}, "SequentialThinking": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-sequential-thinking"]}}}