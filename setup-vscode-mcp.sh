#!/bin/bash

# Bash script to set up MCP services for VS Code (cross-platform)
# Run this script as: bash setup-vscode-mcp.sh

echo "🚀 Setting up MCP Services for VS Code"
echo "======================================="

# Check if Node.js is installed
if ! command -v node &> /dev/null; then
    echo "❌ Node.js is not installed. Please install Node.js first."
    echo "   Download from: https://nodejs.org/"
    exit 1
fi

echo "✅ Node.js is installed: $(node --version)"

# Detect OS and set paths accordingly
if [[ "$OSTYPE" == "msys" || "$OSTYPE" == "win32" || "$OSTYPE" == "cygwin" ]]; then
    # Windows paths
    VSCODE_STABLE_PATH="$APPDATA/Code/User/settings.json"
    VSCODE_INSIDERS_PATH="$APPDATA/Code - Insiders/User/settings.json"
elif [[ "$OSTYPE" == "darwin"* ]]; then
    # macOS paths
    VSCODE_STABLE_PATH="$HOME/Library/Application Support/Code/User/settings.json"
    VSCODE_INSIDERS_PATH="$HOME/Library/Application Support/Code - Insiders/User/settings.json"
else
    # Linux paths
    VSCODE_STABLE_PATH="$HOME/.config/Code/User/settings.json"
    VSCODE_INSIDERS_PATH="$HOME/.config/Code - Insiders/User/settings.json"
fi

# Current project path
CURRENT_PATH=$(pwd)
echo "📁 Current project directory: $CURRENT_PATH"

# Function to update VS Code settings
update_vscode_settings() {
    local settings_path="$1"
    local version_name="$2"
    
    echo "🔧 Configuring $version_name..."
    
    # Create directory if it doesn't exist
    local settings_dir=$(dirname "$settings_path")
    if [ ! -d "$settings_dir" ]; then
        mkdir -p "$settings_dir"
        echo "   Created settings directory: $settings_dir"
    fi
    
    # Create or update settings.json
    local temp_settings=$(mktemp)
    
    # Base MCP configuration
    cat > "$temp_settings" << EOF
{
  "chat.agent.enabled": true,
  "mcp.servers": {
    "context7": {
      "command": "npx",
      "args": ["-y", "@upstash/context7-mcp@latest"],
      "env": {},
      "description": "Up-to-date documentation for libraries and frameworks"
    },
    "filesystem": {
      "command": "npx",
      "args": ["-y", "@modelcontextprotocol/server-filesystem", "$CURRENT_PATH"],
      "env": {},
      "description": "File system operations for the project"
    },
    "git": {
      "command": "npx",
      "args": ["-y", "@modelcontextprotocol/server-git"],
      "env": {},
      "description": "Git operations and repository information"
    },
    "postgres": {
      "command": "npx",
      "args": ["-y", "@modelcontextprotocol/server-postgres"],
      "env": {
        "POSTGRES_CONNECTION_STRING": "*************************************************"
      },
      "description": "PostgreSQL database queries and schema information"
    },
    "sequential-thinking": {
      "command": "npx",
      "args": ["-y", "@modelcontextprotocol/server-sequential-thinking"],
      "env": {},
      "description": "Enhanced reasoning and step-by-step thinking"
    },
    "fetch": {
      "command": "npx",
      "args": ["-y", "@modelcontextprotocol/server-fetch"],
      "env": {},
      "description": "HTTP requests and API interactions"
    }
  },
  "mcp.logging.level": "info",
  "mcp.timeout": 30000,
  "mcp.retries": 3
}
EOF
    
    # If settings file exists, try to merge (basic merge)
    if [ -f "$settings_path" ]; then
        echo "   Backing up existing settings..."
        cp "$settings_path" "$settings_path.backup.$(date +%Y%m%d-%H%M%S)"
    fi
    
    # Copy new settings
    cp "$temp_settings" "$settings_path"
    rm "$temp_settings"
    
    if [ $? -eq 0 ]; then
        echo "   ✅ $version_name configured successfully"
        return 0
    else
        echo "   ❌ Failed to update $version_name settings"
        return 1
    fi
}

# Configure VS Code Stable
stable_configured=false
if command -v code &> /dev/null; then
    if update_vscode_settings "$VSCODE_STABLE_PATH" "VS Code Stable"; then
        stable_configured=true
    fi
else
    echo "⚠️ VS Code Stable not found in PATH"
fi

# Configure VS Code Insiders
insiders_configured=false
if command -v code-insiders &> /dev/null; then
    if update_vscode_settings "$VSCODE_INSIDERS_PATH" "VS Code Insiders"; then
        insiders_configured=true
    fi
else
    echo "⚠️ VS Code Insiders not found in PATH"
fi

# Install useful VS Code extensions
echo ""
echo "🔌 Installing useful VS Code extensions..."

extensions=(
    "ms-python.python"
    "ms-python.debugpy"
    "ms-python.pylint"
    "ms-python.black-formatter"
    "ms-vscode.vscode-json"
    "redhat.vscode-yaml"
    "GitHub.copilot"
    "GitHub.copilot-chat"
    "ms-vscode.vscode-typescript-next"
    "bradlc.vscode-tailwindcss"
    "ms-vscode.vscode-eslint"
)

for extension in "${extensions[@]}"; do
    echo "   Installing $extension..."
    
    # Install for stable if available
    if [ "$stable_configured" = true ]; then
        code --install-extension "$extension" --force > /dev/null 2>&1
    fi
    
    # Install for insiders if available
    if [ "$insiders_configured" = true ]; then
        code-insiders --install-extension "$extension" --force > /dev/null 2>&1
    fi
done

# Pre-install key MCP packages
echo ""
echo "📦 Pre-installing key MCP packages..."

key_packages=(
    "@upstash/context7-mcp@latest"
    "@modelcontextprotocol/server-filesystem"
    "@modelcontextprotocol/server-git"
    "@modelcontextprotocol/server-postgres"
)

for package in "${key_packages[@]}"; do
    echo "   Pre-installing $package..."
    timeout 30 npx -y "$package" --help > /dev/null 2>&1 || echo "     (Will install on first use)"
done

# Summary
echo ""
echo "📊 Setup Summary"
echo "================="

if [ "$stable_configured" = true ]; then
    echo "✅ VS Code Stable: Configured with MCP services"
else
    echo "❌ VS Code Stable: Not configured"
fi

if [ "$insiders_configured" = true ]; then
    echo "✅ VS Code Insiders: Configured with MCP services"
else
    echo "❌ VS Code Insiders: Not configured"
fi

echo ""
echo "🎯 Next Steps:"
echo "1. Restart VS Code (both versions if you have them)"
echo "2. Open your project in VS Code"
echo "3. Open the Command Palette (Ctrl+Shift+P)"
echo "4. Look for 'Chat: ' commands to start using MCP-enhanced AI"
echo "5. Try: 'Create a Django view with error handling. use context7'"

echo ""
echo "🔧 Configured MCP Services:"
echo "- Context7: Up-to-date documentation"
echo "- Filesystem: Project file operations"
echo "- Git: Repository information"
echo "- PostgreSQL: Database queries"
echo "- Sequential Thinking: Enhanced reasoning"
echo "- Fetch: HTTP requests and API interactions"

echo ""
echo "🎉 MCP setup complete!"
