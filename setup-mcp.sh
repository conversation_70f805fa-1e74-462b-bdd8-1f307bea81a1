#!/bin/bash

# MCP Services Setup Script
# This script helps set up MCP servers for enhanced AI development assistance

echo "🚀 Setting up MCP Services for Enhanced AI Development"
echo "======================================================"

# Check if Node.js is installed
if ! command -v node &> /dev/null; then
    echo "❌ Node.js is not installed. Please install Node.js first."
    echo "   Download from: https://nodejs.org/"
    exit 1
fi

echo "✅ Node.js is installed: $(node --version)"

# Check if npm is available
if ! command -v npm &> /dev/null; then
    echo "❌ npm is not available. Please install npm."
    exit 1
fi

echo "✅ npm is available: $(npm --version)"

# Pre-install MCP servers to cache them (with timeout to avoid hanging)
echo ""
echo "📦 Pre-installing MCP servers..."

install_with_timeout() {
    local package=$1
    local timeout=30
    echo "  Installing $package..."

    # Use timeout command if available, otherwise just run normally
    if command -v timeout &> /dev/null; then
        timeout $timeout npx -y $package --help > /dev/null 2>&1 || echo "    (Skipped - may install on first use)"
    else
        # For Windows/systems without timeout, use a background process
        npx -y $package --help > /dev/null 2>&1 &
        local pid=$!
        sleep 5
        if kill -0 $pid 2>/dev/null; then
            kill $pid 2>/dev/null
            echo "    (Skipped - will install on first use)"
        fi
        wait $pid 2>/dev/null || true
    fi
}

install_with_timeout "@upstash/context7-mcp@latest"
install_with_timeout "@modelcontextprotocol/server-filesystem"
install_with_timeout "@modelcontextprotocol/server-git"
install_with_timeout "@modelcontextprotocol/server-postgres"
# Skip memory MCP as it often hangs
echo "  Skipping Memory MCP (known to hang - will install on demand)"
install_with_timeout "@modelcontextprotocol/server-sequential-thinking"

echo "✅ MCP servers installation attempted (some may install on first use)"

# Detect the current directory
CURRENT_DIR=$(pwd)
echo ""
echo "📁 Current project directory: $CURRENT_DIR"

# Create configuration files with correct paths
echo ""
echo "📝 Creating MCP configuration files..."

# Update the filesystem path in configurations
sed -i "s|d:\\\\mark_\\\\Documents\\\\JW|$CURRENT_DIR|g" mcp-configs/cursor-mcp.json
sed -i "s|d:\\\\mark_\\\\Documents\\\\JW|$CURRENT_DIR|g" mcp-configs/vscode-settings.json
sed -i "s|d:\\\\mark_\\\\Documents\\\\JW|$CURRENT_DIR|g" mcp-configs/claude-desktop-config.json

echo "✅ Configuration files updated with correct paths"

# Display setup instructions
echo ""
echo "🎯 MCP Setup Instructions"
echo "========================="
echo ""
echo "Choose your editor and follow the instructions:"
echo ""

echo "📝 FOR CURSOR:"
echo "1. Copy the contents of mcp-configs/cursor-mcp.json"
echo "2. Go to Cursor Settings -> MCP -> Add new global MCP server"
echo "3. Or paste into ~/.cursor/mcp.json"
echo ""

echo "📝 FOR VS CODE:"
echo "1. Copy the contents of mcp-configs/vscode-settings.json"
echo "2. Add to your VS Code settings.json file"
echo "3. Or use the VS Code MCP extension"
echo ""

echo "📝 FOR CLAUDE DESKTOP:"
echo "1. Copy the contents of mcp-configs/claude-desktop-config.json"
echo "2. Paste into your claude_desktop_config.json file"
echo "3. Restart Claude Desktop"
echo ""

echo "🔧 OPTIONAL SERVICES:"
echo "- Brave Search: Get a free API key from https://api.search.brave.com/"
echo "- Update the BRAVE_API_KEY in your chosen configuration file"
echo ""

echo "🎉 MCP Services Setup Complete!"
echo ""
echo "💡 USAGE TIPS:"
echo "- Add 'use context7' to your prompts for up-to-date documentation"
echo "- Use filesystem MCP for file operations"
echo "- Use git MCP for version control operations"
echo "- Use postgres MCP for database queries"
echo "- Use memory MCP for persistent context across conversations"
echo ""

echo "🧪 TEST YOUR SETUP:"
echo "Try this prompt: 'Create a Django view with error handling. use context7'"
echo ""

echo "📚 For more information, see MCP_SETUP_GUIDE.md"
