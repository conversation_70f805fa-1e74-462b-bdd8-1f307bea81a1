# 🚀 MCP Quick Reference Card

## **Accessing AI Chat in VS Code**
- **Command Palette**: `Ctrl+Shift+P` → Type `Chat:`
- **Keyboard Shortcut**: `Ctrl+Alt+I` (if available)
- **Sidebar**: Look for Chat/Copilot icon

## **🔥 Power Prompts to Try**

### **Database Queries**
```
Show me all students with active block bookings
```
```
What's the total revenue from block bookings this month?
```
```
Find students who haven't had lessons in the last 30 days
```

### **Code Analysis**
```
Analyze the lesson_create view for potential improvements
```
```
Show me all error handling decorators used in the project
```
```
List all Django models and their relationships
```

### **Documentation & Best Practices**
```
Create a Django REST API endpoint for student management. use context7
```
```
Show me the latest Django security best practices. use context7
```
```
How to implement proper database indexing in Django? use context7
```

### **File Operations**
```
Find all TODO comments in Python files
```
```
Show me the structure of the JW app directory
```
```
List all views that don't have error handling
```

### **Git & Version Control**
```
Show me recent changes to the block booking system
```
```
What files were modified in the last commit?
```
```
Analyze the commit history for performance improvements
```

### **Complex Multi-Tool Queries**
```
Analyze the current block booking implementation, check for recent changes in git, query the database for usage patterns, and suggest optimizations using current Django best practices. use context7
```

## **🎯 MCP Servers Available**

| Server | Purpose | Example Use |
|--------|---------|-------------|
| **Context7** | Up-to-date docs | `use context7` for current Django docs |
| **Filesystem** | File operations | Browse, read, analyze project files |
| **Git** | Version control | Commit history, changes, branches |
| **PostgreSQL** | Database queries | Real-time data access and analysis |
| **Sequential Thinking** | Enhanced reasoning | Complex problem solving |
| **Fetch** | HTTP requests | API testing, web scraping |

## **💡 Pro Tips**

### **For Better Results**
- Be specific about what you want to achieve
- Mention `use context7` for up-to-date documentation
- Ask for database queries to get real-time data
- Request file analysis for code insights

### **Combining Tools**
- Database + Code analysis: "Query active students and show their related code"
- Git + Documentation: "Show recent changes and best practices for that code"
- Files + Database: "Analyze views that interact with the Student model"

### **Troubleshooting**
- If MCP doesn't work: Restart VS Code
- If database fails: Check connection to **********:5432
- If slow responses: MCP packages install on first use

## **🔧 Quick Commands**

### **VS Code Commands**
- `Ctrl+Shift+P` → `Chat: Open Chat`
- `Ctrl+Shift+P` → `Chat: Start New Chat`
- `Ctrl+Shift+P` → `Developer: Reload Window` (if MCP issues)

### **Test Your Setup**
```bash
python test-mcp-setup.py
```

### **Re-run Setup**
```bash
bash setup-vscode-mcp.sh
```

## **🎉 You're All Set!**

Your VS Code now has superpowers:
- ✅ Real-time database access
- ✅ Up-to-date documentation
- ✅ Complete codebase awareness
- ✅ Version control insights
- ✅ Enhanced AI reasoning

**Start with simple queries and work up to complex multi-tool requests!**
