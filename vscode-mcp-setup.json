{"chat.agent.enabled": true, "mcp.servers": {"context7": {"command": "npx", "args": ["-y", "@upstash/context7-mcp@latest"], "env": {}, "description": "Up-to-date documentation for libraries and frameworks"}, "filesystem": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-filesystem", "d:\\mark_\\Documents\\JW"], "env": {}, "description": "File system operations for the project"}, "git": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-git"], "env": {}, "description": "Git operations and repository information"}, "postgres": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-postgres"], "env": {"POSTGRES_CONNECTION_STRING": "**********************************************/JW"}, "description": "PostgreSQL database queries and schema information"}, "brave-search": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-brave-search"], "env": {"BRAVE_API_KEY": "your_brave_api_key_here"}, "description": "Web search for current information (requires Brave API key)"}, "sequential-thinking": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-sequential-thinking"], "env": {}, "description": "Enhanced reasoning and step-by-step thinking"}, "sqlite": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-sqlite"], "env": {}, "description": "SQLite database operations (for local development)"}, "puppeteer": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-puppeteer"], "env": {}, "description": "Web scraping and browser automation"}, "fetch": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-fetch"], "env": {}, "description": "HTTP requests and API interactions"}, "everything": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-everything"], "env": {}, "description": "Windows Everything search integration (Windows only)"}}, "mcp.logging.level": "info", "mcp.timeout": 30000, "mcp.retries": 3}