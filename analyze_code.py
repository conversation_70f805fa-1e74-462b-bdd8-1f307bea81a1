#!/usr/bin/env python3
"""
Code analysis utility for the Django project.
Provides insights and suggestions for code improvement.
"""

import os
import ast
import glob
from collections import defaultdict, Counter

def analyze_django_project():
    """Analyze the Django project structure and code quality."""
    
    print("🔍 Analyzing Django Project...")
    
    # Find Python files
    python_files = glob.glob('**/*.py', recursive=True)
    python_files = [f for f in python_files if '__pycache__' not in f and 'venv' not in f]
    
    stats = {
        'total_files': len(python_files),
        'total_lines': 0,
        'views': 0,
        'models': 0,
        'forms': 0,
        'functions': 0,
        'classes': 0,
        'imports': defaultdict(int)
    }
    
    for file_path in python_files:
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
                lines = content.split('\n')
                stats['total_lines'] += len(lines)
                
                # Parse AST
                try:
                    tree = ast.parse(content)
                    
                    for node in ast.walk(tree):
                        if isinstance(node, ast.FunctionDef):
                            stats['functions'] += 1
                            if 'views.py' in file_path:
                                stats['views'] += 1
                        elif isinstance(node, ast.ClassDef):
                            stats['classes'] += 1
                            if 'models.py' in file_path:
                                stats['models'] += 1
                            elif 'forms.py' in file_path:
                                stats['forms'] += 1
                        elif isinstance(node, ast.Import):
                            for alias in node.names:
                                stats['imports'][alias.name] += 1
                        elif isinstance(node, ast.ImportFrom):
                            if node.module:
                                stats['imports'][node.module] += 1
                
                except SyntaxError:
                    pass
                    
        except Exception as e:
            print(f"Error analyzing {file_path}: {e}")
    
    # Print results
    print(f"\n📊 Project Statistics:")
    print(f"   Total Python files: {stats['total_files']}")
    print(f"   Total lines of code: {stats['total_lines']:,}")
    print(f"   Functions: {stats['functions']}")
    print(f"   Classes: {stats['classes']}")
    print(f"   Views: {stats['views']}")
    print(f"   Models: {stats['models']}")
    print(f"   Forms: {stats['forms']}")
    
    print(f"\n📚 Most Used Imports:")
    for module, count in Counter(stats['imports']).most_common(10):
        print(f"   {module}: {count}")

if __name__ == "__main__":
    analyze_django_project()
