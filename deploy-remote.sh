#!/bin/bash

# Remote deployment script for LTDWJ Django application
# This script deploys to a remote Linux server via SSH

echo "🚀 Starting Remote LTDWJ Django Deployment..."
echo "=============================================="

# Configuration
REMOTE_HOST="**********"  # Your remote server IP
REMOTE_USER="root"        # Or your SSH user
REMOTE_PATH="/opt/ltdwj"  # Remote deployment path
PROJECT_NAME="ltdwj-app"
CONTAINER_NAME="LTDWJ-$(date +%d%m%Y)"

# Check if .env file exists
if [ ! -f .env ]; then
    echo "❌ .env file not found. Please create one using .env.template as reference."
    exit 1
fi

echo "✅ Found .env file"

# Load environment variables from .env file
source .env

# Override DEBUG to False for production deployment
export DEBUG=False

# Generate a new secret key for production if not set
if [ -z "$SECRET_KEY" ] || [ "$SECRET_KEY" = "your_secret_key_here" ]; then
    export SECRET_KEY=$(openssl rand -base64 32)
    echo "🔑 Generated new SECRET_KEY for production"
fi

# Use environment variables for deployment settings
DEPLOY_PORT=${CONTAINER_PORT:-8002}
DEPLOY_HOST=${HOST_IP:-**********}

echo "📋 Deployment Configuration:"
echo "   Remote Host: $REMOTE_HOST"
echo "   Remote Path: $REMOTE_PATH"
echo "   Container: $CONTAINER_NAME"
echo "   Port: $DEPLOY_HOST:$DEPLOY_PORT"

# Check SSH connectivity
echo ""
echo "🔐 Testing SSH connectivity..."
if ssh -o ConnectTimeout=10 -o BatchMode=yes $REMOTE_USER@$REMOTE_HOST exit 2>/dev/null; then
    echo "✅ SSH connection successful"
else
    echo "❌ SSH connection failed. Please check:"
    echo "   - SSH key is set up for $REMOTE_USER@$REMOTE_HOST"
    echo "   - Remote server is accessible"
    echo "   - Correct username and IP address"
    exit 1
fi

# Create deployment package
echo ""
echo "📦 Creating deployment package..."
TEMP_DIR=$(mktemp -d)
PACKAGE_NAME="ltdwj-deployment-$(date +%Y%m%d-%H%M%S).tar.gz"

# Copy necessary files
cp -r . "$TEMP_DIR/ltdwj/"
cd "$TEMP_DIR"

# Remove unnecessary files from package
rm -rf ltdwj/.git
rm -rf ltdwj/__pycache__
rm -rf ltdwj/*/__pycache__
rm -rf ltdwj/venv
rm -rf ltdwj/env
rm -rf ltdwj/node_modules
rm -rf ltdwj/chroma_db
rm -rf ltdwj/*.log
rm -rf ltdwj/mcp-configs
rm -f ltdwj/setup-*.sh
rm -f ltdwj/test-*.py
rm -f ltdwj/analyze-*.py

# Create package
tar -czf "$PACKAGE_NAME" ltdwj/
echo "✅ Created deployment package: $PACKAGE_NAME"

# Transfer package to remote server
echo ""
echo "📤 Transferring package to remote server..."
scp "$PACKAGE_NAME" $REMOTE_USER@$REMOTE_HOST:/tmp/
if [ $? -eq 0 ]; then
    echo "✅ Package transferred successfully"
else
    echo "❌ Package transfer failed"
    rm -rf "$TEMP_DIR"
    exit 1
fi

# Execute remote deployment
echo ""
echo "🚀 Executing remote deployment..."

ssh $REMOTE_USER@$REMOTE_HOST << EOF
set -e

echo "🔧 Setting up remote environment..."

# Create deployment directory
mkdir -p $REMOTE_PATH
cd $REMOTE_PATH

# Backup existing deployment if it exists
if [ -d "ltdwj" ]; then
    echo "📦 Backing up existing deployment..."
    mv ltdwj ltdwj-backup-\$(date +%Y%m%d-%H%M%S)
fi

# Extract new deployment
echo "📂 Extracting new deployment..."
tar -xzf /tmp/$PACKAGE_NAME
rm /tmp/$PACKAGE_NAME

cd ltdwj

# Check if Docker is installed
if ! command -v docker &> /dev/null; then
    echo "🐳 Installing Docker..."
    curl -fsSL https://get.docker.com -o get-docker.sh
    sh get-docker.sh
    systemctl enable docker
    systemctl start docker
    rm get-docker.sh
fi

# Check if Docker Compose is installed
if ! command -v docker-compose &> /dev/null; then
    echo "🐙 Installing Docker Compose..."
    curl -L "https://github.com/docker/compose/releases/latest/download/docker-compose-\$(uname -s)-\$(uname -m)" -o /usr/local/bin/docker-compose
    chmod +x /usr/local/bin/docker-compose
fi

echo "✅ Docker environment ready"

# Stop and remove old container if it exists
OLD_CONTAINER=\$(docker ps -a --format "table {{.Names}}" | grep "LTDWJ-" | head -1)
if [ ! -z "\$OLD_CONTAINER" ]; then
    echo "🛑 Stopping old container: \$OLD_CONTAINER"
    docker stop \$OLD_CONTAINER 2>/dev/null || true
    docker rm \$OLD_CONTAINER 2>/dev/null || true
fi

# Build new Docker image
echo "🔨 Building Docker image..."
docker build -t $PROJECT_NAME .

# Create and start new container
echo "🚀 Starting new container: $CONTAINER_NAME"
docker run -d \\
    --name $CONTAINER_NAME \\
    --restart always \\
    -p $DEPLOY_HOST:$DEPLOY_PORT:8000 \\
    -v \$(pwd)/staticfiles:/app/staticfiles \\
    -v \$(pwd)/media:/app/media \\
    -e DEBUG=False \\
    -e SECRET_KEY="$SECRET_KEY" \\
    -e DB_NAME="$DB_NAME" \\
    -e DB_USER="$DB_USER" \\
    -e DB_PASSWORD="$DB_PASSWORD" \\
    -e DB_HOST="$DB_HOST" \\
    -e DB_PORT="$DB_PORT" \\
    -e GOOGLE_OAUTH2_CLIENT_ID="$GOOGLE_OAUTH2_CLIENT_ID" \\
    -e GOOGLE_OAUTH2_CLIENT_SECRET="$GOOGLE_OAUTH2_CLIENT_SECRET" \\
    -e ALLOWED_HOSTS="$ALLOWED_HOSTS" \\
    -e LOG_LEVEL="$LOG_LEVEL" \\
    $PROJECT_NAME

# Wait a moment for container to start
sleep 5

# Check container status
if docker ps | grep -q $CONTAINER_NAME; then
    echo "✅ Container $CONTAINER_NAME is running successfully"
    
    # Show container logs
    echo ""
    echo "📋 Container logs (last 20 lines):"
    docker logs --tail 20 $CONTAINER_NAME
    
    # Test the application
    echo ""
    echo "🧪 Testing application..."
    sleep 10
    if curl -f -s http://localhost:$DEPLOY_PORT/ > /dev/null; then
        echo "✅ Application is responding on port $DEPLOY_PORT"
    else
        echo "⚠️ Application may still be starting up"
    fi
    
else
    echo "❌ Container failed to start"
    echo "📋 Container logs:"
    docker logs $CONTAINER_NAME
    exit 1
fi

echo ""
echo "🎉 Deployment completed successfully!"
echo "📍 Application URL: http://$DEPLOY_HOST:$DEPLOY_PORT"
echo "🐳 Container Name: $CONTAINER_NAME"
echo "📁 Deployment Path: $REMOTE_PATH/ltdwj"

EOF

# Cleanup local temp files
rm -rf "$TEMP_DIR"

echo ""
echo "🎉 Remote deployment completed!"
echo "📍 Your application should be available at: http://$DEPLOY_HOST:$DEPLOY_PORT"
echo ""
echo "🔧 Useful remote commands:"
echo "   View logs: ssh $REMOTE_USER@$REMOTE_HOST 'docker logs $CONTAINER_NAME'"
echo "   Restart:   ssh $REMOTE_USER@$REMOTE_HOST 'docker restart $CONTAINER_NAME'"
echo "   Stop:      ssh $REMOTE_USER@$REMOTE_HOST 'docker stop $CONTAINER_NAME'"
echo "   Shell:     ssh $REMOTE_USER@$REMOTE_HOST 'docker exec -it $CONTAINER_NAME bash'"
