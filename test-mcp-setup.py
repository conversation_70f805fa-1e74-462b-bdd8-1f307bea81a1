#!/usr/bin/env python3
"""
Test script to validate MCP setup and configuration.
Verifies that all MCP services are properly configured and accessible.
"""

import os
import json
import subprocess
import sys
from pathlib import Path
import platform

class MCPTester:
    def __init__(self):
        self.test_results = []
        self.os_type = platform.system().lower()
        
    def log_test_result(self, test_name, passed, message=""):
        result = "✅ PASS" if passed else "❌ FAIL"
        self.test_results.append((test_name, passed, message))
        print(f"{result}: {test_name}")
        if message:
            print(f"    {message}")
    
    def test_node_js_availability(self):
        """Test if Node.js is available and working."""
        print("\n🔍 Testing Node.js Availability...")
        
        try:
            result = subprocess.run(['node', '--version'], capture_output=True, text=True, timeout=10)
            if result.returncode == 0:
                version = result.stdout.strip()
                self.log_test_result("Node.js Available", True, f"Version: {version}")
                return True
            else:
                self.log_test_result("Node.js Available", False, "Node.js not found or not working")
                return False
        except Exception as e:
            self.log_test_result("Node.js Available", False, str(e))
            return False
    
    def test_npm_packages(self):
        """Test if key MCP packages can be accessed."""
        print("\n📦 Testing MCP Package Accessibility...")
        
        packages_to_test = [
            "@upstash/context7-mcp",
            "@modelcontextprotocol/server-filesystem",
            "@modelcontextprotocol/server-git",
            "@modelcontextprotocol/server-postgres"
        ]
        
        accessible_packages = 0
        for package in packages_to_test:
            try:
                # Try to run the package with --help to see if it's accessible
                result = subprocess.run(
                    ['npx', '-y', package, '--help'], 
                    capture_output=True, 
                    text=True, 
                    timeout=30
                )
                if result.returncode == 0 or "help" in result.stdout.lower() or "usage" in result.stdout.lower():
                    accessible_packages += 1
                    print(f"    ✅ {package}: Accessible")
                else:
                    print(f"    ⚠️ {package}: May install on first use")
            except subprocess.TimeoutExpired:
                print(f"    ⚠️ {package}: Timeout (will install on first use)")
            except Exception as e:
                print(f"    ❌ {package}: Error - {str(e)}")
        
        success_rate = accessible_packages / len(packages_to_test)
        self.log_test_result(
            "MCP Packages Accessible", 
            success_rate >= 0.5,  # At least 50% should be accessible
            f"{accessible_packages}/{len(packages_to_test)} packages accessible"
        )
    
    def test_vscode_configuration(self):
        """Test VS Code MCP configuration files."""
        print("\n🔧 Testing VS Code Configuration...")
        
        # Determine VS Code settings paths based on OS
        if self.os_type == "windows":
            settings_paths = [
                Path(os.environ.get('APPDATA', '')) / 'Code' / 'User' / 'settings.json',
                Path(os.environ.get('APPDATA', '')) / 'Code - Insiders' / 'User' / 'settings.json'
            ]
        elif self.os_type == "darwin":  # macOS
            home = Path.home()
            settings_paths = [
                home / 'Library' / 'Application Support' / 'Code' / 'User' / 'settings.json',
                home / 'Library' / 'Application Support' / 'Code - Insiders' / 'User' / 'settings.json'
            ]
        else:  # Linux
            home = Path.home()
            settings_paths = [
                home / '.config' / 'Code' / 'User' / 'settings.json',
                home / '.config' / 'Code - Insiders' / 'User' / 'settings.json'
            ]
        
        configured_versions = 0
        for i, settings_path in enumerate(settings_paths):
            version_name = "VS Code Stable" if i == 0 else "VS Code Insiders"
            
            if settings_path.exists():
                try:
                    with open(settings_path, 'r', encoding='utf-8') as f:
                        settings = json.load(f)
                    
                    # Check for MCP configuration
                    has_mcp_servers = 'mcp.servers' in settings
                    has_agent_enabled = settings.get('chat.agent.enabled', False)
                    
                    if has_mcp_servers and has_agent_enabled:
                        configured_versions += 1
                        mcp_server_count = len(settings['mcp.servers'])
                        print(f"    ✅ {version_name}: Configured with {mcp_server_count} MCP servers")
                    else:
                        print(f"    ❌ {version_name}: MCP not properly configured")
                        
                except Exception as e:
                    print(f"    ❌ {version_name}: Error reading settings - {str(e)}")
            else:
                print(f"    ⚠️ {version_name}: Settings file not found")
        
        self.log_test_result(
            "VS Code MCP Configuration",
            configured_versions > 0,
            f"{configured_versions} VS Code version(s) configured"
        )
    
    def test_project_files(self):
        """Test if project-specific MCP files exist."""
        print("\n📁 Testing Project MCP Files...")
        
        expected_files = [
            'vscode-mcp-setup.json',
            'vscode-mcp-enhanced.json',
            'setup-vscode-mcp.sh',
            'setup-vscode-mcp.ps1',
            '.cursorrules',
            '.windsurfrules'
        ]
        
        existing_files = 0
        for file_name in expected_files:
            if Path(file_name).exists():
                existing_files += 1
                print(f"    ✅ {file_name}: Found")
            else:
                print(f"    ❌ {file_name}: Missing")
        
        self.log_test_result(
            "Project MCP Files",
            existing_files >= len(expected_files) * 0.7,  # At least 70% should exist
            f"{existing_files}/{len(expected_files)} files found"
        )
    
    def test_database_connectivity(self):
        """Test database connectivity for PostgreSQL MCP server."""
        print("\n🗄️ Testing Database Connectivity...")
        
        try:
            # Try to import psycopg2 and test connection
            import psycopg2
            
            conn_string = "**********************************************/JW"
            
            try:
                conn = psycopg2.connect(conn_string)
                conn.close()
                self.log_test_result("Database Connectivity", True, "PostgreSQL connection successful")
            except psycopg2.OperationalError as e:
                self.log_test_result("Database Connectivity", False, f"Connection failed: {str(e)}")
            except Exception as e:
                self.log_test_result("Database Connectivity", False, f"Unexpected error: {str(e)}")
                
        except ImportError:
            self.log_test_result("Database Connectivity", False, "psycopg2 not installed")
    
    def test_git_repository(self):
        """Test if we're in a Git repository for Git MCP server."""
        print("\n📚 Testing Git Repository...")
        
        try:
            result = subprocess.run(['git', 'status'], capture_output=True, text=True, timeout=10)
            if result.returncode == 0:
                self.log_test_result("Git Repository", True, "Git repository detected")
            else:
                self.log_test_result("Git Repository", False, "Not a Git repository")
        except Exception as e:
            self.log_test_result("Git Repository", False, str(e))
    
    def test_filesystem_access(self):
        """Test filesystem access for filesystem MCP server."""
        print("\n📂 Testing Filesystem Access...")
        
        current_dir = Path.cwd()
        
        # Test basic file operations
        try:
            # Check if we can list files
            files = list(current_dir.glob('*.py'))
            
            # Check if we can read a file
            if files:
                test_file = files[0]
                with open(test_file, 'r', encoding='utf-8') as f:
                    content = f.read(100)  # Read first 100 chars
                
                self.log_test_result(
                    "Filesystem Access", 
                    True, 
                    f"Can access {len(files)} Python files in project"
                )
            else:
                self.log_test_result("Filesystem Access", False, "No Python files found")
                
        except Exception as e:
            self.log_test_result("Filesystem Access", False, str(e))
    
    def test_additional_tools(self):
        """Test additional development tools."""
        print("\n🛠️ Testing Additional Development Tools...")
        
        tools_to_test = [
            ('python', 'Python interpreter'),
            ('pip', 'Python package manager'),
            ('docker', 'Docker (optional)'),
            ('redis-server', 'Redis server (optional)')
        ]
        
        available_tools = 0
        for tool, description in tools_to_test:
            try:
                result = subprocess.run([tool, '--version'], capture_output=True, text=True, timeout=10)
                if result.returncode == 0:
                    available_tools += 1
                    print(f"    ✅ {description}: Available")
                else:
                    print(f"    ❌ {description}: Not available")
            except:
                print(f"    ❌ {description}: Not available")
        
        self.log_test_result(
            "Additional Tools",
            available_tools >= 2,  # At least Python and pip should be available
            f"{available_tools}/{len(tools_to_test)} tools available"
        )
    
    def run_all_tests(self):
        """Run all MCP setup tests."""
        print("🧪 Starting MCP Setup Validation")
        print("=" * 40)
        
        # Run all tests
        node_available = self.test_node_js_availability()
        
        if node_available:
            self.test_npm_packages()
        else:
            print("⚠️ Skipping package tests due to Node.js unavailability")
        
        self.test_vscode_configuration()
        self.test_project_files()
        self.test_database_connectivity()
        self.test_git_repository()
        self.test_filesystem_access()
        self.test_additional_tools()
        
        # Summary
        print("\n📊 MCP Setup Test Results")
        print("=" * 30)
        
        passed = sum(1 for _, result, _ in self.test_results if result)
        total = len(self.test_results)
        
        print(f"Total Tests: {total}")
        print(f"Passed: {passed}")
        print(f"Failed: {total - passed}")
        print(f"Success Rate: {(passed/total)*100:.1f}%")
        
        if passed >= total * 0.8:  # 80% success rate
            print("\n🎉 MCP setup is working well! You're ready for enhanced AI assistance.")
            print("\n🚀 Try these commands in VS Code:")
            print("   - Open Command Palette (Ctrl+Shift+P)")
            print("   - Type 'Chat: ' to see available AI commands")
            print("   - Try: 'Create a Django view with error handling. use context7'")
        elif passed >= total * 0.6:  # 60% success rate
            print("\n⚠️ MCP setup is partially working. Some features may not be available.")
            print("   Consider rerunning the setup scripts or checking the failed tests.")
        else:
            print("\n❌ MCP setup has significant issues. Please review the failed tests.")
            print("   You may need to reinstall or reconfigure MCP services.")
        
        return passed >= total * 0.6

if __name__ == "__main__":
    tester = MCPTester()
    success = tester.run_all_tests()
    sys.exit(0 if success else 1)
