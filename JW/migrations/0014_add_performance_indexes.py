# Generated migration for performance indexes

from django.db import migrations


def add_indexes_if_tables_exist(apps, schema_editor):
    """Add indexes only if the tables exist"""
    db_alias = schema_editor.connection.alias

    # List of indexes to create with their table checks
    indexes = [
        ("app_jw_lesson", "CREATE INDEX IF NOT EXISTS idx_lesson_date ON app_jw_lesson(date);"),
        ("app_jw_lesson", "CREATE INDEX IF NOT EXISTS idx_lesson_student ON app_jw_lesson(student_id);"),
        ("app_jw_lesson", "CREATE INDEX IF NOT EXISTS idx_lesson_date_student ON app_jw_lesson(date, student_id);"),
        ("app_jw_businessexpense", "CREATE INDEX IF NOT EXISTS idx_expense_date ON app_jw_businessexpense(date);"),
        ("app_jw_mileage", "CREATE INDEX IF NOT EXISTS idx_mileage_date ON app_jw_mileage(date);"),
        ("app_jw_student", "CREATE INDEX IF NOT EXISTS idx_student_area ON app_jw_student(area);"),
    ]

    with schema_editor.connection.cursor() as cursor:
        for table_name, index_sql in indexes:
            # Check if table exists
            cursor.execute("""
                SELECT EXISTS (
                    SELECT FROM information_schema.tables
                    WHERE table_schema = 'public'
                    AND table_name = %s
                );
            """, [table_name])

            table_exists = cursor.fetchone()[0]
            if table_exists:
                try:
                    cursor.execute(index_sql)
                    print(f"Created index for table {table_name}")
                except Exception as e:
                    print(f"Warning: Could not create index for {table_name}: {e}")
            else:
                print(f"Skipping index creation for {table_name} - table does not exist")


def remove_indexes(apps, schema_editor):
    """Remove the indexes"""
    indexes_to_drop = [
        "DROP INDEX IF EXISTS idx_lesson_date;",
        "DROP INDEX IF EXISTS idx_lesson_student;",
        "DROP INDEX IF EXISTS idx_lesson_date_student;",
        "DROP INDEX IF EXISTS idx_expense_date;",
        "DROP INDEX IF EXISTS idx_mileage_date;",
        "DROP INDEX IF EXISTS idx_student_area;",
    ]

    with schema_editor.connection.cursor() as cursor:
        for drop_sql in indexes_to_drop:
            try:
                cursor.execute(drop_sql)
            except Exception as e:
                print(f"Warning: Could not drop index: {e}")


class Migration(migrations.Migration):

    dependencies = [
        ('JW', '0013_create_test_centres_for_all_users'),
    ]

    operations = [
        migrations.RunPython(add_indexes_if_tables_exist, remove_indexes),
    ]
