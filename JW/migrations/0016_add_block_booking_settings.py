# Generated manually for block booking control settings

from django.db import migrations

class Migration(migrations.Migration):

    dependencies = [
        ('JW', '0015_lesson_performance_indexes'),
    ]

    operations = [
        migrations.RunSQL(
            sql='''
            -- Add block booking overdraft setting to user settings
            ALTER TABLE "APP_JW_user_settings"
            ADD COLUMN IF NOT EXISTS allow_block_booking_overdraft BOOLEAN NOT NULL DEFAULT FALSE;
            
            -- Add block booking disabled setting to student table
            ALTER TABLE "APP_JW_student"
            ADD COLUMN IF NOT EXISTS block_booking_disabled BOOLEAN NOT NULL DEFAULT FALSE;
            
            -- Add comments for documentation
            COMMENT ON COLUMN "APP_JW_user_settings".allow_block_booking_overdraft IS 'Allow lessons to be recorded even when block booking credit is insufficient';
            COMMENT ON COLUMN "APP_JW_student".block_booking_disabled IS 'Disable block booking for this student (pay per lesson)';
            ''',
            reverse_sql='''
            -- Remove the new columns (for rollback)
            ALTER TABLE "APP_JW_user_settings" DROP COLUMN IF EXISTS allow_block_booking_overdraft;
            ALTER TABLE "APP_JW_student" DROP COLUMN IF EXISTS block_booking_disabled;
            '''
        ),
    ]
