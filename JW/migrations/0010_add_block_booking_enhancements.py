from django.db import migrations, models

class Migration(migrations.Migration):
    dependencies = [
        ('JW', '0008_appjwblockbooking_appjwblockbookingusage_and_more'),
    ]

    operations = [
        migrations.RunSQL(
            sql='''
            -- Add new fields to block booking table
            ALTER TABLE "APP_JW_block_booking"
            ADD COLUMN IF NOT EXISTS price_per_lesson_fixed DECIMAL(5, 2) NULL;

            ALTER TABLE "APP_JW_block_booking"
            ADD COLUMN IF NOT EXISTS remainder_balance DECIMAL(6, 2) NOT NULL DEFAULT 0;

            ALTER TABLE "APP_JW_block_booking"
            ADD COLUMN IF NOT EXISTS calculation_method VARCHAR(10) NOT NULL DEFAULT 'legacy';
            ''',
            reverse_sql='''
            -- Remove new fields (for rollback)
            ALTER TABLE "APP_JW_block_booking" DROP COLUMN IF EXISTS price_per_lesson_fixed;
            ALTER TABLE "APP_JW_block_booking" DROP COLUMN IF EXISTS remainder_balance;
            ALTER TABLE "APP_JW_block_booking" DROP COLUMN IF EXISTS calculation_method;
            '''
        ),
    ]
