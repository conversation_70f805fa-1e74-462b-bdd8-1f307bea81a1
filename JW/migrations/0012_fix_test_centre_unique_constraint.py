# Generated manually to fix test centre unique constraint

from django.db import migrations


class Migration(migrations.Migration):

    dependencies = [
        ('JW', '0011_add_test_centres'),
    ]

    operations = [
        # Drop the existing unique constraint on name
        migrations.RunSQL(
            """
            ALTER TABLE "APP_JW_test_centre" 
            DROP CONSTRAINT IF EXISTS "APP_JW_test_centre_name_key";
            """,
            reverse_sql="""
            ALTER TABLE "APP_JW_test_centre" 
            ADD CONSTRAINT "APP_JW_test_centre_name_key" UNIQUE (name);
            """
        ),
        
        # Add unique constraint on name + user_id
        migrations.RunSQL(
            """
            ALTER TABLE "APP_JW_test_centre" 
            ADD CONSTRAINT "APP_JW_test_centre_name_user_unique" 
            UNIQUE (name, user_id);
            """,
            reverse_sql="""
            ALTER TABLE "APP_JW_test_centre" 
            DROP CONSTRAINT IF EXISTS "APP_JW_test_centre_name_user_unique";
            """
        ),
    ]
