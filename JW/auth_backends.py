from django.contrib.auth.backends import BaseBackend
from django.contrib.auth.models import User
from django.conf import settings
from django.shortcuts import redirect
from django.contrib import messages
from django.urls import reverse
from allauth.socialaccount.models import SocialAccount
from allauth.exceptions import ImmediateHttpResponse
import logging

logger = logging.getLogger(__name__)

class GoogleEmailMappingBackend(BaseBackend):
    """
    Custom authentication backend that maps specific Google emails 
    to the existing 'john' user account.
    """
    
    def authenticate(self, request, **credentials):
        """
        This backend doesn't handle direct authentication,
        but works with allauth's social authentication.
        """
        return None
    
    def get_user(self, user_id):
        """
        Get user by ID - standard implementation.
        """
        try:
            return User.objects.get(pk=user_id)
        except User.DoesNotExist:
            return None

def map_google_user_to_john(sender, request, sociallogin, **kwargs):
    """
    Signal handler that maps allowed Google emails to the 'john' user.
    This is called during the social login process.
    """
    if sociallogin.account.provider != 'google':
        return
    
    email = sociallogin.account.extra_data.get('email', '').lower()
    allowed_emails = [email.lower() for email in settings.ALLOWED_GOOGLE_EMAILS]
    
    logger.info(f"Google login attempt with email: {email}")
    
    if email not in allowed_emails:
        logger.warning(f"Unauthorized Google login attempt with email: {email}")
        # Redirect to unauthorized page with error message
        messages.error(
            request,
            'Unauthorized access. Your Google account is not authorized to access this system. '
            'Please contact the administrator if you believe this is an error.'
        )
        raise ImmediateHttpResponse(redirect('account_login'))
    
    try:
        # Get the target user (john)
        target_user = User.objects.get(username=settings.GOOGLE_AUTH_TARGET_USERNAME)
        
        # Map this social account to the target user
        sociallogin.user = target_user
        
        # Check if this social account already exists
        try:
            existing_social_account = SocialAccount.objects.get(
                provider='google',
                uid=sociallogin.account.uid
            )
            # Update the existing account to point to john
            existing_social_account.user = target_user
            existing_social_account.save()
            logger.info(f"Updated existing social account for {email} to map to {target_user.username}")
        except SocialAccount.DoesNotExist:
            # This is a new social account, it will be created and linked to john
            logger.info(f"New social account for {email} will be mapped to {target_user.username}")
        
        # Add success message
        messages.success(
            request,
            f'Successfully logged in as {email}. You now have access to John\'s driving school data.'
        )
        
    except User.DoesNotExist:
        logger.error(f"Target user '{settings.GOOGLE_AUTH_TARGET_USERNAME}' not found")
        messages.error(
            request,
            'System configuration error: Target user not found. Please contact the administrator.'
        )
        raise ImmediateHttpResponse(redirect('account_login'))

# Connect the signal
from allauth.socialaccount.signals import pre_social_login
pre_social_login.connect(map_google_user_to_john)