from django.db import models
from django.contrib.auth.models import User
from .lesson import AppJwLesson

class AppJwTestCentre(models.Model):
    id = models.BigAutoField(primary_key=True)
    name = models.CharField(max_length=100)
    user = models.ForeignKey(User, on_delete=models.CASCADE)
    created_at = models.DateTimeField(auto_now_add=True)
    active = models.BooleanField(default=True)

    class Meta:
        db_table = 'APP_JW_test_centre'
        ordering = ['name']
        unique_together = ['name', 'user']  # Unique per user, not globally

    def __str__(self):
        return self.name

class AppJwFailedTest(models.Model):
    student_name = models.CharField(max_length=30)
    test_date = models.DateField()
    lesson = models.ForeignKey(AppJwLesson, models.DO_NOTHING, null=True)
    test_centre = models.ForeignKey(AppJwTestCentre, models.DO_NOTHING, null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        managed = False
        db_table = 'APP_JW_failed_test'

class AppJwReportHeader(models.Model):
    user = models.ForeignKey(User, on_delete=models.CASCADE)
    field_name = models.CharField(max_length=50)  # e.g., 'name', 'address', etc.
    field_value = models.CharField(max_length=255)
    is_enabled = models.BooleanField(default=False)
    display_order = models.IntegerField(default=0)

    class Meta:
        db_table = 'APP_JW_report_header'
        unique_together = ['user', 'field_name']

class AppJwUserSettings(models.Model):
    user = models.OneToOneField(User, on_delete=models.CASCADE, related_name='jw_settings')
    tax_ni_percentage = models.DecimalField(max_digits=5, decimal_places=2, default=21.00)
    allow_block_booking_overdraft = models.BooleanField(
        default=False,
        help_text="Allow lessons to be recorded even when block booking credit is insufficient"
    )

    class Meta:
        db_table = 'APP_JW_user_settings'
