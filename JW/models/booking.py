from django.db import models, transaction
from django.contrib.auth.models import User
from decimal import Decimal, ROUND_HALF_UP
from .student import AppJwStudent
from .lesson import AppJwLesson

class AppJwBlockBooking(models.Model):
    id = models.BigAutoField(primary_key=True)
    student = models.ForeignKey(AppJwStudent, models.DO_NOTHING, db_column='student_id', related_name='block_bookings')
    date_created = models.DateField()
    amount_paid = models.DecimalField(max_digits=6, decimal_places=2)
    total_lessons = models.DecimalField(max_digits=4, decimal_places=1)
    lessons_used = models.DecimalField(max_digits=4, decimal_places=1, default=0)
    active = models.BooleanField(default=True)
    notes = models.TextField(blank=True, null=True)
    # Temporary field to force migration
    # temp_field = models.CharField(max_length=1, blank=True, null=True)

    # New fields for enhanced block booking system
    price_per_lesson_fixed = models.DecimalField(max_digits=5, decimal_places=2, null=True, blank=True)
    remainder_balance = models.DecimalField(max_digits=6, decimal_places=2, default=0)
    calculation_method = models.CharField(max_length=10, default='legacy', choices=[
        ('legacy', 'Legacy (Amount ÷ Lessons)'),
        ('new', 'New (Amount ÷ Price + Remainder)')
    ])

    class Meta:
        managed = False
        db_table = 'APP_JW_block_booking'
        ordering = ['-date_created']

    @property
    def lessons_remaining(self):
        return self.total_lessons - self.lessons_used

    @property
    def price_per_lesson(self):
        """Get price per lesson based on calculation method, rounded to nearest 0.50 for legacy."""
        if self.calculation_method == 'new' and self.price_per_lesson_fixed is not None:
            return self.price_per_lesson_fixed
        elif self.total_lessons and self.total_lessons > 0:
            # Legacy calculation: amount_paid ÷ total_lessons
            raw_price = self.amount_paid / self.total_lessons
            # Round to nearest 0.50
            return (raw_price * 2).quantize(Decimal('1'), rounding=ROUND_HALF_UP) / 2
        return Decimal('0.00')

    @property
    def is_fully_used(self):
        return self.lessons_used >= self.total_lessons

    @property
    def total_value_remaining(self):
        """Total value including lessons + remainder balance"""
        lessons_value = self.lessons_remaining * self.price_per_lesson
        return lessons_value + (self.remainder_balance or Decimal('0.00'))

    @property
    def effective_lessons_remaining(self):
        """Lessons remaining including remainder balance converted to lessons"""
        if self.price_per_lesson > 0:
            remainder_as_lessons = (self.remainder_balance or Decimal('0.00')) / self.price_per_lesson
            return self.lessons_remaining + remainder_as_lessons
        return self.lessons_remaining

    def save(self, *args, **kwargs):
        # Only calculate remainder_balance for legacy method if not explicitly set
        if self.calculation_method == 'legacy' and self.amount_paid is not None and self.total_lessons is not None:
            if self.total_lessons > 0:
                # Calculate price per lesson rounded to 2 decimal places
                calculated_price_per_lesson = (self.amount_paid / self.total_lessons).quantize(Decimal('0.01'))
                # Calculate the value covered by the whole/half lessons
                covered_value = self.total_lessons * calculated_price_per_lesson
                self.remainder_balance = self.amount_paid - covered_value
            else:
                self.remainder_balance = self.amount_paid # If no lessons, all is remainder

        # For 'new' calculation method, don't override remainder_balance unless it's None
        elif self.calculation_method == 'new' and self.remainder_balance is None:
            # Calculate remainder for new method: amount_paid - (total_lessons * price_per_lesson_fixed)
            if (self.amount_paid is not None and self.total_lessons is not None and
                self.price_per_lesson_fixed is not None):
                lesson_cost = self.total_lessons * self.price_per_lesson_fixed
                self.remainder_balance = self.amount_paid - lesson_cost
            else:
                self.remainder_balance = Decimal('0.00')

        # Ensure remainder_balance is set to 0 if None (but allow negative values for overpayments)
        if self.remainder_balance is None:
            self.remainder_balance = Decimal('0.00')

        super().save(*args, **kwargs)

    def add_usage(self, hours_to_add, price_per_hour_of_lesson):
        """
        Adds usage to the block booking, adjusting lessons_used and remainder_balance.
        Returns True if successful, False if not enough credit.
        Uses SELECT FOR UPDATE to prevent race conditions.
        """
        from decimal import Decimal
        from django.core.exceptions import ValidationError

        try:
            with transaction.atomic():
                # Use SELECT FOR UPDATE to prevent race conditions
                booking = AppJwBlockBooking.objects.select_for_update().get(pk=self.pk)

                # Calculate the value of the current lesson
                value_to_add = Decimal(str(hours_to_add)) * Decimal(str(price_per_hour_of_lesson))

                # Check if there's enough total value remaining
                if value_to_add > booking.total_value_remaining:
                    return False # Not enough credit

                if booking.calculation_method == 'new':
                    # Use lesson allocation first, then remainder for any excess
                    if booking.price_per_lesson_fixed and booking.price_per_lesson_fixed > 0:
                        # Calculate how much lesson allocation is available
                        lessons_available = booking.total_lessons - booking.lessons_used
                        lesson_value_available = lessons_available * booking.price_per_lesson_fixed

                        if value_to_add <= lesson_value_available:
                            # Can be covered entirely by lesson allocation
                            hours_from_lessons = value_to_add / booking.price_per_lesson_fixed
                            booking.lessons_used += hours_from_lessons
                        else:
                            # Use all remaining lesson allocation, then remainder for excess
                            booking.lessons_used = booking.total_lessons  # Use all lessons
                            excess_value = value_to_add - lesson_value_available

                            # Deduct excess from remainder
                            if booking.remainder_balance >= excess_value:
                                booking.remainder_balance -= excess_value
                            else:
                                # Not enough total credit - this should have been caught earlier
                                raise ValidationError("Insufficient credit for this lesson")
                    else:
                        # Fallback or error if price_per_lesson_fixed is not set for 'new' method
                        raise ValidationError("price_per_lesson_fixed must be set for 'new' calculation method.")
                else: # legacy method
                    # Directly deduct hours from total_lessons
                    booking.lessons_used += Decimal(str(hours_to_add))

                # Ensure lessons_used does not exceed total_lessons (can happen with floating point arithmetic or edge cases)
                if booking.lessons_used > booking.total_lessons:
                    booking.lessons_used = booking.total_lessons

                # Deactivate if fully used (lessons_used >= total_lessons and remainder_balance is zero or negligible)
                if booking.lessons_used >= booking.total_lessons and booking.remainder_balance <= Decimal('0.01'): # Allow for tiny floating point remainders
                    booking.active = False

                booking.save()

                # Update self with the new values
                self.lessons_used = booking.lessons_used
                self.remainder_balance = booking.remainder_balance
                self.active = booking.active

                return True

        except (ValidationError, ValueError) as e:
            # Log the error for debugging
            import logging
            logger = logging.getLogger(__name__)
            logger.error(f"Error in add_usage for booking {self.pk}: {str(e)}")
            return False
        except Exception as e:
            # Log unexpected errors
            import logging
            logger = logging.getLogger(__name__)
            logger.error(f"Unexpected error in add_usage for booking {self.pk}: {str(e)}")
            return False

    def remove_usage(self, hours_to_remove, price_per_hour_of_lesson):
        """
        Removes usage from the block booking, returning credit.
        Uses SELECT FOR UPDATE to prevent race conditions.
        """
        from decimal import Decimal

        try:
            with transaction.atomic():
                # Use SELECT FOR UPDATE to prevent race conditions
                booking = AppJwBlockBooking.objects.select_for_update().get(pk=self.pk)

                if booking.calculation_method == 'new':
                    value_to_return = Decimal(str(hours_to_remove)) * Decimal(str(price_per_hour_of_lesson))

                    # Return credit by reversing the add_usage logic:
                    # First try to return to lesson allocation, then to remainder
                    if booking.lessons_used >= booking.total_lessons:
                        # All lessons were used, so this removal should go to remainder
                        booking.remainder_balance += value_to_return
                        # Also free up some lesson allocation if possible
                        hours_to_free = min(Decimal(str(hours_to_remove)), booking.lessons_used)
                        booking.lessons_used -= hours_to_free
                    else:
                        # Some lesson allocation is still available, return to lesson allocation
                        if booking.price_per_lesson_fixed and booking.price_per_lesson_fixed > 0:
                            hours_to_return = value_to_return / booking.price_per_lesson_fixed
                            booking.lessons_used -= hours_to_return
                            if booking.lessons_used < 0:
                                # Overage goes to remainder
                                overage_hours = abs(booking.lessons_used)
                                overage_value = overage_hours * booking.price_per_lesson_fixed
                                booking.remainder_balance += overage_value
                                booking.lessons_used = Decimal('0.00')
                        else:
                            # Fallback for missing price_per_lesson_fixed
                            booking.lessons_used -= Decimal(str(hours_to_remove))
                            if booking.lessons_used < 0:
                                booking.lessons_used = Decimal('0.00')
                else: # legacy method
                    booking.lessons_used -= Decimal(str(hours_to_remove))
                    if booking.lessons_used < 0:
                        booking.lessons_used = Decimal('0.00') # Prevent negative usage

                # Reactivate the block booking if it was previously deactivated
                booking.active = True
                booking.save()

                # Update self with the new values
                self.lessons_used = booking.lessons_used
                self.remainder_balance = booking.remainder_balance
                self.active = booking.active

        except Exception as e:
            # Log the error for debugging
            import logging
            logger = logging.getLogger(__name__)
            logger.error(f"Error in remove_usage for booking {self.pk}: {str(e)}")
            raise

class AppJwBlockBookingUsage(models.Model):
    id = models.BigAutoField(primary_key=True)
    block_booking = models.ForeignKey(AppJwBlockBooking, models.DO_NOTHING, related_name='usages')
    lesson = models.ForeignKey(AppJwLesson, models.DO_NOTHING, related_name='block_booking_usage')
    lessons_used = models.DecimalField(max_digits=4, decimal_places=1)
    date_used = models.DateField()

    class Meta:
        managed = False
        db_table = 'APP_JW_block_booking_usage'
        ordering = ['-date_used']
