from django.db import models
from django.contrib.auth.models import User
from decimal import Decimal
from .student import AppJwStudent

class AppJwLesson(models.Model):
    id = models.BigAutoField(primary_key=True)
    day_of_week = models.CharField(max_length=10)
    date = models.DateField()
    lesson_hours = models.DecimalField(max_digits=4, decimal_places=1)
    student_name = models.CharField(max_length=30)
    notes = models.TextField(blank=True, null=True)
    user = models.ForeignKey(User, models.DO_NOTHING, db_column='user_id')
    price_per_hour = models.DecimalField(max_digits=5, decimal_places=2)
    amount = models.DecimalField(max_digits=6, decimal_places=2, blank=True, null=True)
    student = models.ForeignKey(
        AppJwStudent,
        models.DO_NOTHING,
        db_column='student_id',
        related_name='lessons'
    )

    class Meta:
        managed = False
        db_table = 'APP_JW_lesson'
        ordering = ['-date']

    @property
    def calculated_amount(self):
        if self.lesson_hours and self.price_per_hour:
            return Decimal(str(self.lesson_hours)) * Decimal(str(self.price_per_hour))
        return Decimal('0.00')

    def save(self, *args, **kwargs):
        # Calculate amount
        if self.lesson_hours and self.price_per_hour:
            self.amount = Decimal(str(self.lesson_hours)) * Decimal(str(self.price_per_hour))
        super().save(*args, **kwargs)
