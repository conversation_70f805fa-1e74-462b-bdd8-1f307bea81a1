from django.db import models
from django.contrib.auth.models import User
import calendar

class AppJwMileage(models.Model):
    date = models.DateField()
    day = models.CharField(max_length=10, blank=True)
    mileage_type = models.CharField(max_length=10, choices=[('Business', 'Business'), ('Personal', 'Personal')])
    miles = models.DecimalField(max_digits=5, decimal_places=1)
    notes = models.TextField(blank=True, null=True)

    class Meta:
        db_table = 'app_jw_mileage'
        managed = False

    def save(self, *args, **kwargs):
        if self.date and not self.day:
            self.day = calendar.day_name[self.date.weekday()]
        super().save(*args, **kwargs)

    def __str__(self):
        return f"{self.date} - {self.miles} miles ({self.mileage_type})"

class AppJwBusinessmileage(models.Model):
    id = models.BigAutoField(primary_key=True)
    day_of_week = models.CharField(max_length=10)
    date = models.DateField()
    mileage = models.DecimalField(max_digits=5, decimal_places=1)
    notes = models.TextField(blank=True, null=True)
    user = models.ForeignKey(User, models.DO_NOTHING, db_column='user_id')

    class Meta:
        managed = False
        db_table = 'APP_JW_businessmileage'
        ordering = ['-date']

    def save(self, *args, **kwargs):
        # Set day of week
        if self.date:
            self.day_of_week = calendar.day_name[self.date.weekday()]
        super().save(*args, **kwargs)

class AppJwPersonalmileage(models.Model):
    id = models.BigAutoField(primary_key=True)
    day_of_week = models.CharField(max_length=10)
    date = models.DateField()
    mileage = models.DecimalField(max_digits=5, decimal_places=1)
    notes = models.TextField(blank=True, null=True)
    user = models.ForeignKey(User, models.DO_NOTHING, db_column='user_id')

    class Meta:
        managed = False
        db_table = 'APP_JW_personalmileage'
        ordering = ['-date']

    def save(self, *args, **kwargs):
        # Set day of week
        if self.date:
            self.day_of_week = calendar.day_name[self.date.weekday()]
        super().save(*args, **kwargs)
