from django.core.management.base import BaseCommand
from django.contrib.auth.models import User
from django.contrib.sites.models import Site
from django.conf import settings
from allauth.socialaccount.models import SocialApp
import os

class Command(BaseCommand):
    help = 'Set up Google OAuth integration for the driving school app'

    def add_arguments(self, parser):
        parser.add_argument(
            '--client-id',
            type=str,
            help='Google OAuth Client ID',
        )
        parser.add_argument(
            '--client-secret',
            type=str,
            help='Google OAuth Client Secret',
        )
        parser.add_argument(
            '--create-john-user',
            action='store_true',
            help='Create the john user if it doesn\'t exist',
        )

    def handle(self, *args, **options):
        self.stdout.write(self.style.SUCCESS('Setting up Google OAuth integration...'))
        
        # Ensure the john user exists
        if options['create_john_user']:
            self.create_john_user()
        
        # Check if john user exists
        try:
            john_user = User.objects.get(username='john')
            self.stdout.write(self.style.SUCCESS(f'Target user "john" found (ID: {john_user.id})'))
        except User.DoesNotExist:
            self.stdout.write(
                self.style.ERROR(
                    'Target user "john" not found. Run with --create-john-user to create it.'
                )
            )
            return
        
        # Set up Site
        site, created = Site.objects.get_or_create(
            id=1,
            defaults={
                'domain': 'learntodrivewithjohn.co.uk',
                'name': 'Learn to Drive with John'
            }
        )
        if created:
            self.stdout.write(self.style.SUCCESS('Created Site object'))
        else:
            self.stdout.write(self.style.SUCCESS('Site object already exists'))
        
        # Set up Google OAuth app
        client_id = options['client_id'] or os.getenv('GOOGLE_OAUTH2_CLIENT_ID')
        client_secret = options['client_secret'] or os.getenv('GOOGLE_OAUTH2_CLIENT_SECRET')
        
        if not client_id or not client_secret:
            self.stdout.write(
                self.style.ERROR(
                    'Google OAuth credentials not provided. '
                    'Use --client-id and --client-secret or set GOOGLE_OAUTH2_CLIENT_ID and GOOGLE_OAUTH2_CLIENT_SECRET environment variables.'
                )
            )
            return
        
        # Create or update Google OAuth app
        google_app, created = SocialApp.objects.get_or_create(
            provider='google',
            defaults={
                'name': 'Google OAuth',
                'client_id': client_id,
                'secret': client_secret,
            }
        )
        
        if not created:
            google_app.client_id = client_id
            google_app.secret = client_secret
            google_app.save()
            self.stdout.write(self.style.SUCCESS('Updated Google OAuth app'))
        else:
            self.stdout.write(self.style.SUCCESS('Created Google OAuth app'))
        
        # Add site to the app
        google_app.sites.add(site)
        
        self.stdout.write(self.style.SUCCESS('Google OAuth setup complete!'))
        self.stdout.write(
            self.style.WARNING(
                f'Authorized emails: {", ".join(settings.ALLOWED_GOOGLE_EMAILS)}'
            )
        )
        self.stdout.write(
            self.style.WARNING(
                'Make sure to add the following redirect URI to your Google OAuth app:'
            )
        )
        self.stdout.write(
            self.style.WARNING(
                f'https://{site.domain}/accounts/google/login/callback/'
            )
        )
    
    def create_john_user(self):
        """Create the john user if it doesn't exist"""
        try:
            User.objects.get(username='john')
            self.stdout.write(self.style.SUCCESS('User "john" already exists'))
        except User.DoesNotExist:
            john_user = User.objects.create_user(
                username='john',
                email='<EMAIL>',
                first_name='John',
                last_name='Driving Instructor',
                is_staff=True,
                is_superuser=True
            )
            john_user.set_password('changeme123!')  # Set a default password
            john_user.save()
            self.stdout.write(
                self.style.SUCCESS(
                    'Created user "john" with password "changeme123!" - please change this password!'
                )
            )