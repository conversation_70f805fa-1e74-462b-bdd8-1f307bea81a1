"""
Django management command to check application health and performance.
Usage: python manage.py monitor_health
"""

from django.core.management.base import BaseCommand
from django.utils import timezone
from JW.utils.monitoring import <PERSON>Checker, PerformanceMonitor, DatabaseMonitor
from JW.models import AppJwStudent, AppJwLesson, AppJwBlockBooking
import json

class Command(BaseCommand):
    help = 'Monitor application health and performance metrics'

    def add_arguments(self, parser):
        parser.add_argument(
            '--format',
            type=str,
            default='text',
            choices=['text', 'json'],
            help='Output format (text or json)'
        )
        parser.add_argument(
            '--check-db',
            action='store_true',
            help='Check database health'
        )
        parser.add_argument(
            '--check-cache',
            action='store_true',
            help='Check cache health'
        )
        parser.add_argument(
            '--stats',
            action='store_true',
            help='Show application statistics'
        )

    def handle(self, *args, **options):
        results = {}
        
        if options['check_db'] or not any([options['check_db'], options['check_cache'], options['stats']]):
            results['database'] = HealthChecker.check_database_health()
        
        if options['check_cache'] or not any([options['check_db'], options['check_cache'], options['stats']]):
            results['cache'] = HealthChecker.check_cache_health()
        
        if options['stats'] or not any([options['check_db'], options['check_cache'], options['stats']]):
            results['statistics'] = self.get_app_statistics()
        
        if options['format'] == 'json':
            self.stdout.write(json.dumps(results, indent=2, default=str))
        else:
            self.print_text_results(results)

    def get_app_statistics(self):
        """Get application statistics."""
        try:
            stats = {
                'students': {
                    'total': AppJwStudent.objects.count(),
                    'active': AppJwStudent.objects.filter(active='Yes').count(),
                    'passed': AppJwStudent.objects.filter(active='Passed').count(),
                },
                'lessons': {
                    'total': AppJwLesson.objects.count(),
                    'this_month': AppJwLesson.objects.filter(
                        date__gte=timezone.now().replace(day=1)
                    ).count(),
                },
                'block_bookings': {
                    'total': AppJwBlockBooking.objects.count(),
                    'active': AppJwBlockBooking.objects.filter(active=True).count(),
                },
                'timestamp': timezone.now().isoformat()
            }
            return stats
        except Exception as e:
            return {'error': str(e)}

    def print_text_results(self, results):
        """Print results in text format."""
        self.stdout.write(self.style.SUCCESS('=== Application Health Monitor ==='))
        
        if 'database' in results:
            db_result = results['database']
            status_style = self.style.SUCCESS if db_result['status'] == 'healthy' else self.style.ERROR
            self.stdout.write(f"\nDatabase: {status_style(db_result['status'].upper())}")
            if db_result.get('response_time'):
                self.stdout.write(f"  Response Time: {db_result['response_time']:.3f}s")
            if db_result.get('error'):
                self.stdout.write(f"  Error: {db_result['error']}")
        
        if 'cache' in results:
            cache_result = results['cache']
            status_style = self.style.SUCCESS if cache_result['status'] == 'healthy' else self.style.ERROR
            self.stdout.write(f"\nCache: {status_style(cache_result['status'].upper())}")
            if cache_result.get('error'):
                self.stdout.write(f"  Error: {cache_result['error']}")
        
        if 'statistics' in results:
            stats = results['statistics']
            if 'error' not in stats:
                self.stdout.write(f"\n{self.style.SUCCESS('Application Statistics:')}")
                self.stdout.write(f"  Students: {stats['students']['total']} total, {stats['students']['active']} active, {stats['students']['passed']} passed")
                self.stdout.write(f"  Lessons: {stats['lessons']['total']} total, {stats['lessons']['this_month']} this month")
                self.stdout.write(f"  Block Bookings: {stats['block_bookings']['total']} total, {stats['block_bookings']['active']} active")
            else:
                self.stdout.write(f"  Statistics Error: {stats['error']}")
        
        self.stdout.write(f"\n{self.style.SUCCESS('Health check completed at:')} {timezone.now()}")
