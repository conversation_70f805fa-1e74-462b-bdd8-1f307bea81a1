from django.contrib import messages
from django.db import IntegrityError, DatabaseError, OperationalError
from django.core.exceptions import ValidationError
from django.http import JsonResponse
from django.shortcuts import redirect
import logging

logger = logging.getLogger(__name__)

class FormValidationMiddleware:
    def __init__(self, get_response):
        self.get_response = get_response

    def __call__(self, request):
        response = self.get_response(request)
        return response

    def process_exception(self, request, exception):
        """Enhanced exception handling with better error messages and logging."""

        # Log all exceptions for debugging
        logger.error(f"Exception in middleware: {type(exception).__name__}: {str(exception)}")

        is_ajax = request.headers.get('x-requested-with') == 'XMLHttpRequest'

        if isinstance(exception, OperationalError):
            # Database connection issues
            error_message = 'Database connection error. Please try again in a moment.'
            if is_ajax:
                return JsonResponse({'error': error_message, 'error_code': 'DB_CONNECTION_ERROR'}, status=503)
            messages.error(request, error_message)
            return redirect('dashboard')

        elif isinstance(exception, DatabaseError):
            # General database errors
            error_message = 'A database error occurred. Please contact support if this persists.'
            if is_ajax:
                return JsonResponse({'error': error_message, 'error_code': 'DB_ERROR'}, status=500)
            messages.error(request, error_message)
            return redirect('dashboard')

        elif isinstance(exception, IntegrityError):
            # Handle specific integrity constraint violations
            error_str = str(exception).lower()
            if 'unique' in error_str:
                if 'student_name' in error_str:
                    error_message = 'A student with this name already exists.'
                elif 'mobile_number' in error_str:
                    error_message = 'This mobile number is already registered.'
                else:
                    error_message = 'This information already exists in the database.'
            elif 'mobile_number_check' in error_str:
                error_message = 'Invalid mobile number format. Please enter a valid UK mobile number.'
            else:
                error_message = 'There was an error saving the data. Please check your information.'

            if is_ajax:
                return JsonResponse({'error': error_message, 'error_code': 'INTEGRITY_ERROR'}, status=400)
            messages.error(request, error_message)
            return None  # Let the view handle the response

        elif isinstance(exception, ValidationError):
            # Form validation errors
            error_message = str(exception) if str(exception) else 'Please check that you have entered data in the correct format'
            if is_ajax:
                return JsonResponse({'error': error_message, 'error_code': 'VALIDATION_ERROR'}, status=400)
            messages.error(request, error_message)
            return None  # Let the view handle the response

        # For any other exceptions, let Django handle them normally
        return None
