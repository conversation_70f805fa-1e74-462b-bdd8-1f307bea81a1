"""
Performance monitoring and metrics collection utilities.
"""

import time
import logging
from functools import wraps
from typing import Dict, Any, Optional
from django.core.cache import cache
from django.db import connection
from django.conf import settings
from datetime import datetime, timedelta
import json

logger = logging.getLogger(__name__)

class PerformanceMonitor:
    """Performance monitoring utility class."""
    
    @staticmethod
    def log_performance_metric(metric_name: str, value: float, tags: Optional[Dict[str, str]] = None):
        """Log a performance metric."""
        metric_data = {
            'metric': metric_name,
            'value': value,
            'timestamp': datetime.now().isoformat(),
            'tags': tags or {}
        }
        logger.info(f"PERFORMANCE_METRIC: {json.dumps(metric_data)}")
    
    @staticmethod
    def log_database_query_count():
        """Log the current database query count."""
        query_count = len(connection.queries)
        PerformanceMonitor.log_performance_metric('database_queries', query_count)
        return query_count
    
    @staticmethod
    def get_cache_stats():
        """Get cache statistics if available."""
        try:
            # This works with Redis cache backend
            cache_stats = {
                'hits': cache._cache.get_stats().get('hits', 0),
                'misses': cache._cache.get_stats().get('misses', 0),
            }
            return cache_stats
        except:
            return {'hits': 0, 'misses': 0}

def monitor_performance(metric_name: Optional[str] = None):
    """
    Decorator to monitor function performance.
    """
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            start_time = time.time()
            start_queries = len(connection.queries)
            
            try:
                result = func(*args, **kwargs)
                success = True
                error = None
            except Exception as e:
                success = False
                error = str(e)
                raise
            finally:
                end_time = time.time()
                end_queries = len(connection.queries)
                
                execution_time = end_time - start_time
                query_count = end_queries - start_queries
                
                name = metric_name or f"{func.__module__}.{func.__name__}"
                
                # Log performance metrics
                PerformanceMonitor.log_performance_metric(
                    f"{name}.execution_time",
                    execution_time,
                    {'success': success, 'error': error}
                )
                
                PerformanceMonitor.log_performance_metric(
                    f"{name}.query_count",
                    query_count,
                    {'success': success}
                )
                
                # Log slow operations
                if execution_time > 1.0:  # Log operations taking more than 1 second
                    logger.warning(
                        f"SLOW_OPERATION: {name} took {execution_time:.2f}s with {query_count} queries"
                    )
            
            return result
        return wrapper
    return decorator

def monitor_block_booking_operations(func):
    """
    Specific monitoring for block booking operations.
    """
    @wraps(func)
    def wrapper(*args, **kwargs):
        start_time = time.time()
        
        try:
            result = func(*args, **kwargs)
            success = True
        except Exception as e:
            success = False
            logger.error(f"BLOCK_BOOKING_ERROR: {func.__name__} failed: {str(e)}")
            raise
        finally:
            execution_time = time.time() - start_time
            
            # Log block booking metrics
            PerformanceMonitor.log_performance_metric(
                f"block_booking.{func.__name__}",
                execution_time,
                {'success': success}
            )
            
            # Track block booking operation counts
            cache_key = f"block_booking_ops_{datetime.now().strftime('%Y%m%d_%H')}"
            try:
                cache.set(cache_key, cache.get(cache_key, 0) + 1, timeout=3600)
            except:
                pass  # Cache might not be available
        
        return result
    return wrapper

class DatabaseMonitor:
    """Database performance monitoring."""
    
    @staticmethod
    def log_slow_queries(threshold_seconds: float = 0.5):
        """Log queries that exceed the threshold."""
        if not settings.DEBUG:
            return  # Only monitor in debug mode
        
        for query in connection.queries:
            query_time = float(query['time'])
            if query_time > threshold_seconds:
                logger.warning(
                    f"SLOW_QUERY: {query_time:.3f}s - {query['sql'][:200]}..."
                )
    
    @staticmethod
    def get_connection_info():
        """Get database connection information."""
        return {
            'queries_count': len(connection.queries),
            'vendor': connection.vendor,
            'settings': {
                'NAME': connection.settings_dict.get('NAME'),
                'HOST': connection.settings_dict.get('HOST'),
                'PORT': connection.settings_dict.get('PORT'),
            }
        }

class ErrorTracker:
    """Track and analyze errors."""
    
    @staticmethod
    def log_error_with_context(error: Exception, context: Dict[str, Any]):
        """Log an error with additional context."""
        error_data = {
            'error_type': type(error).__name__,
            'error_message': str(error),
            'context': context,
            'timestamp': datetime.now().isoformat()
        }
        logger.error(f"ERROR_WITH_CONTEXT: {json.dumps(error_data)}")
    
    @staticmethod
    def track_error_rate(error_type: str):
        """Track error rates by type."""
        cache_key = f"error_rate_{error_type}_{datetime.now().strftime('%Y%m%d_%H')}"
        try:
            current_count = cache.get(cache_key, 0)
            cache.set(cache_key, current_count + 1, timeout=3600)
        except:
            pass  # Cache might not be available

def log_user_activity(activity_type: str, user_id: int, details: Dict[str, Any]):
    """Log user activity for analytics."""
    activity_data = {
        'activity_type': activity_type,
        'user_id': user_id,
        'details': details,
        'timestamp': datetime.now().isoformat()
    }
    logger.info(f"USER_ACTIVITY: {json.dumps(activity_data)}")

class HealthChecker:
    """Application health checking utilities."""
    
    @staticmethod
    def check_database_health():
        """Check database connectivity and performance."""
        try:
            start_time = time.time()
            with connection.cursor() as cursor:
                cursor.execute("SELECT 1")
                result = cursor.fetchone()
            
            response_time = time.time() - start_time
            
            return {
                'status': 'healthy' if result else 'unhealthy',
                'response_time': response_time,
                'connection_info': DatabaseMonitor.get_connection_info()
            }
        except Exception as e:
            return {
                'status': 'unhealthy',
                'error': str(e),
                'response_time': None
            }
    
    @staticmethod
    def check_cache_health():
        """Check cache connectivity."""
        try:
            test_key = 'health_check_test'
            cache.set(test_key, 'test_value', timeout=60)
            result = cache.get(test_key)
            cache.delete(test_key)
            
            return {
                'status': 'healthy' if result == 'test_value' else 'unhealthy',
                'stats': PerformanceMonitor.get_cache_stats()
            }
        except Exception as e:
            return {
                'status': 'unhealthy',
                'error': str(e)
            }
    
    @staticmethod
    def get_system_health():
        """Get overall system health status."""
        return {
            'database': HealthChecker.check_database_health(),
            'cache': HealthChecker.check_cache_health(),
            'timestamp': datetime.now().isoformat()
        }
