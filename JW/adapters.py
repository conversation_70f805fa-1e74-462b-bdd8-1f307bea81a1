from allauth.account.adapter import DefaultAccountAdapter
from allauth.socialaccount.adapter import DefaultSocialAccountAdapter
from allauth.socialaccount.models import SocialAccount
from django.contrib.auth.models import User
from django.conf import settings
from django.shortcuts import redirect
from django.contrib import messages
from django.urls import reverse
from allauth.exceptions import ImmediateHttpResponse
import logging

logger = logging.getLogger(__name__)

class CustomAccountAdapter(DefaultAccountAdapter):
    """
    Custom account adapter to handle account-related functionality
    """
    def is_open_for_signup(self, request):
        # Disable regular signup - only Google OAuth allowed
        return False

class CustomSocialAccountAdapter(DefaultSocialAccountAdapter):
    """
    Custom social account adapter to handle Google OAuth restrictions
    """
    
    def is_open_for_signup(self, request, sociallogin):
        """
        Only allow signup for authorized Google emails
        """
        if sociallogin.account.provider != 'google':
            return False
            
        email = sociallogin.account.extra_data.get('email', '').lower()
        allowed_emails = [email.lower() for email in settings.ALLOWED_GOOGLE_EMAILS]
        
        return email in allowed_emails
    
    def pre_social_login(self, request, sociallogin):
        """
        Handle the social login process before user creation/login
        """
        if sociallogin.account.provider != 'google':
            return
            
        email = sociallogin.account.extra_data.get('email', '').lower()
        allowed_emails = [email.lower() for email in settings.ALLOWED_GOOGLE_EMAILS]
        
        logger.info(f"Google login attempt with email: {email}")
        
        # Check if email is authorized
        if email not in allowed_emails:
            logger.warning(f"Unauthorized Google login attempt with email: {email}")
            messages.error(
                request,
                'Unauthorized access. Your Google account is not authorized to access this system. '
                'Please contact the administrator if you believe this is an error.'
            )
            raise ImmediateHttpResponse(redirect('/'))
        
        # Get the target user (john)
        try:
            target_user = User.objects.get(username=settings.GOOGLE_AUTH_TARGET_USERNAME)
        except User.DoesNotExist:
            logger.error(f"Target user '{settings.GOOGLE_AUTH_TARGET_USERNAME}' not found")
            messages.error(
                request,
                'System configuration error: Target user not found. Please contact the administrator.'
            )
            raise ImmediateHttpResponse(redirect('/'))
        
        # Map this social login to the target user
        sociallogin.user = target_user
        
        # Check if this social account already exists and update it
        try:
            existing_social_account = SocialAccount.objects.get(
                provider='google',
                uid=sociallogin.account.uid
            )
            existing_social_account.user = target_user
            existing_social_account.save()
            logger.info(f"Updated existing social account for {email} to map to {target_user.username}")
        except SocialAccount.DoesNotExist:
            # This is a new social account, it will be created and linked to john
            logger.info(f"New social account for {email} will be mapped to {target_user.username}")
        
        # Add success message
        name = sociallogin.account.extra_data.get('name', email)
        messages.success(
            request,
            f'Welcome {name}! You are now logged in and have access to the driving school management system.'
        )