{% extends 'JW/base.html' %}

{% block title %}Confirm Delete{% endblock %}

{% block content %}
<div class="card">
    <div class="card-body text-center">
        <h3 class="card-title text-danger mb-4">⚠️ Warning: Deletion Confirmation</h3>
        
        <p class="lead mb-4">Are you <strong>absolutely sure</strong> you want to delete this record?</p>
        
        <div class="alert alert-warning" role="alert">
            <p class="mb-0"><strong>This action cannot be undone!</strong></p>
            <p class="mb-0">Please confirm that you want to permanently delete this record.</p>
        </div>

        <div class="table-responsive mb-4">
            <table class="table table-bordered">
                <thead>
                    <tr>
                        <th>Field</th>
                        <th>Value</th>
                    </tr>
                </thead>
                <tbody>
                    {% for field, value in record_details.items %}
                    <tr>
                        <td><strong>{{ field }}</strong></td>
                        <td>{{ value }}</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>

        {% if test_implications %}
        <div class="alert alert-danger mb-4" role="alert">
            <h5 class="alert-heading">⚠️ Test Record Implications</h5>
            <p class="mb-2">Deleting this lesson will also:</p>
            <ul class="list-unstyled mb-0">
                {% for implication in test_implications %}
                <li class="mb-1">{{ implication }}</li>
                {% endfor %}
            </ul>
        </div>
        {% endif %}

        <form method="post" class="d-flex justify-content-center gap-3">
            {% csrf_token %}
            <a href="{{ cancel_url }}" class="btn btn-secondary">No, Cancel</a>
            <button type="submit" class="btn btn-danger">Yes, Delete This Record</button>
        </form>
    </div>
</div>
{% endblock %}
