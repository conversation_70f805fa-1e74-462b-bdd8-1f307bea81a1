{% extends 'JW/base.html' %}
{% load crispy_forms_tags %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2>{{ title }}</h2>
                <a href="{% url 'student_edit' student.pk %}" class="btn btn-secondary">
                    <i class="fas fa-arrow-left"></i> Back to Student
                </a>
            </div>

            <!-- Warning about existing lessons -->
            {% if related_lessons %}
            <div class="alert alert-warning">
                <h5><i class="fas fa-exclamation-triangle"></i> Important Notice</h5>
                <p>This block booking has been used for <strong>{{ related_lessons.count }}</strong> lesson(s) since {{ booking.date_created|date:"M d, Y" }}.</p>
                <p>Changes to this booking will be validated against existing lesson records. If your changes would conflict with existing lessons, you'll be asked to modify or delete those lessons first.</p>
            </div>
            {% endif %}

            <!-- Current booking summary -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5>Current Booking Summary</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3">
                            <strong>Amount Paid:</strong><br>
                            £{{ booking.amount_paid|floatformat:2 }}
                        </div>
                        <div class="col-md-3">
                            <strong>Total Lessons:</strong><br>
                            {{ booking.total_lessons|floatformat:1 }}
                        </div>
                        <div class="col-md-3">
                            <strong>Lessons Used:</strong><br>
                            {{ booking.lessons_used|floatformat:1 }}
                        </div>
                        <div class="col-md-3">
                            <strong>Remainder Balance:</strong><br>
                            £{{ booking.remainder_balance|floatformat:2 }}
                        </div>
                    </div>
                </div>
            </div>

            <!-- Edit form -->
            <form method="post" id="editBookingForm">
                {% csrf_token %}
                
                <div class="card">
                    <div class="card-header">
                        <h5>Edit Block Booking Details</h5>
                    </div>
                    <div class="card-body">
                        <div class="row g-3">
                            <div class="col-md-6">
                                {{ form.date_created|as_crispy_field }}
                            </div>
                            <div class="col-md-6">
                                {{ form.amount_paid|as_crispy_field }}
                            </div>
                            <div class="col-md-6">
                                {{ form.total_lessons|as_crispy_field }}
                            </div>
                            <div class="col-md-6">
                                {{ form.price_per_lesson_fixed|as_crispy_field }}
                            </div>
                            <div class="col-md-6">
                                {{ form.remainder_balance|as_crispy_field }}
                            </div>
                            <div class="col-md-6">
                                {{ form.calculation_method|as_crispy_field }}
                            </div>
                            <div class="col-12">
                                {{ form.notes|as_crispy_field }}
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Related lessons summary -->
                {% if related_lessons %}
                <div class="card mt-4">
                    <div class="card-header">
                        <h5>Related Lessons ({{ related_lessons.count }})</h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-sm">
                                <thead>
                                    <tr>
                                        <th>Date</th>
                                        <th>Hours</th>
                                        <th>Price/Hour</th>
                                        <th>Amount</th>
                                        <th>Notes</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for lesson in related_lessons|slice:":10" %}
                                    <tr>
                                        <td>{{ lesson.date|date:"M d, Y" }}</td>
                                        <td>{{ lesson.lesson_hours|floatformat:1 }}</td>
                                        <td>£{{ lesson.price_per_hour|floatformat:2 }}</td>
                                        <td>£{{ lesson.amount|floatformat:2 }}</td>
                                        <td>{{ lesson.notes|truncatechars:50 }}</td>
                                    </tr>
                                    {% endfor %}
                                    {% if related_lessons.count > 10 %}
                                    <tr>
                                        <td colspan="5" class="text-muted text-center">
                                            ... and {{ related_lessons.count|add:"-10" }} more lessons
                                        </td>
                                    </tr>
                                    {% endif %}
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
                {% endif %}

                <!-- Action buttons -->
                <div class="mt-4">
                    <div class="row g-2">
                        <div class="col-md-6">
                            <button type="submit" class="btn btn-primary w-100">
                                <i class="fas fa-save"></i> Save Changes
                            </button>
                        </div>
                        <div class="col-md-6">
                            <a href="{% url 'student_edit' student.pk %}" class="btn btn-secondary w-100">
                                <i class="fas fa-times"></i> Cancel
                            </a>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block javascript %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Add form validation
    const form = document.getElementById('editBookingForm');
    const amountPaidInput = document.querySelector('input[name="amount_paid"]');
    const totalLessonsInput = document.querySelector('input[name="total_lessons"]');
    const pricePerLessonInput = document.querySelector('input[name="price_per_lesson_fixed"]');
    const remainderInput = document.querySelector('input[name="remainder_balance"]');
    
    // Calculate remainder when values change
    function calculateRemainder() {
        const amountPaid = parseFloat(amountPaidInput.value) || 0;
        const totalLessons = parseFloat(totalLessonsInput.value) || 0;
        const pricePerLesson = parseFloat(pricePerLessonInput.value) || 0;
        
        if (amountPaid > 0 && totalLessons > 0 && pricePerLesson > 0) {
            const lessonCost = totalLessons * pricePerLesson;
            const remainder = amountPaid - lessonCost;
            remainderInput.value = remainder.toFixed(2);
        }
    }
    
    // Add event listeners
    if (amountPaidInput && totalLessonsInput && pricePerLessonInput && remainderInput) {
        amountPaidInput.addEventListener('input', calculateRemainder);
        totalLessonsInput.addEventListener('input', calculateRemainder);
        pricePerLessonInput.addEventListener('input', calculateRemainder);
    }
    
    // Form submission confirmation
    form.addEventListener('submit', function(e) {
        if (!confirm('Are you sure you want to save these changes? This will update the block booking and may affect lesson calculations.')) {
            e.preventDefault();
        }
    });
});
</script>
{% endblock %}
