{% extends "JW/base.html" %}
{% load socialaccount %}

{% block title %}Learn to Drive with <PERSON>{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-md-6">
        <div class="card mt-5">
            <div class="card-body text-center">
                <h1 class="card-title mb-4">Learn to Drive with John</h1>
                <h5 class="text-muted mb-4">Driving School Management System</h5>
                
                {% if messages %}
                    {% for message in messages %}
                        <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                            {{ message }}
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    {% endfor %}
                {% endif %}
                
                {% if not user.is_authenticated %}
                    <p class="text-muted mb-4">Please sign in to access the system</p>
                    
                    <!-- Google Login Button -->
                    {% get_providers as socialaccount_providers %}
                    {% if socialaccount_providers %}
                        {% for provider in socialaccount_providers %}
                            {% if provider.id == "google" %}
                                <a href="{% provider_login_url 'google' %}" class="btn btn-danger btn-lg mb-3">
                                    <i class="fab fa-google me-2"></i>
                                    Sign in with Google
                                </a>
                            {% endif %}
                        {% endfor %}
                    {% else %}
                        <div class="alert alert-warning">
                            Google OAuth is not configured yet. Please contact the administrator.
                        </div>
                    {% endif %}
                    

                {% else %}
                    <p class="text-success mb-4">Welcome back!</p>
                    <a href="{% url 'dashboard' %}" class="btn btn-primary btn-lg">
                        Go to Dashboard
                    </a>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}