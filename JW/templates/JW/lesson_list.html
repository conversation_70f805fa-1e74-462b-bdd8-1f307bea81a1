{% extends 'JW/base.html' %}

{% block title %}Lessons{% endblock %}

{% block content %}
<div class="container-fluid px-4" style="max-width: 1400px;">
    <!-- Page Header and Stats - Only visible on xl screens -->
    <h2 class="d-none d-xl-block mb-4">Lessons</h2>
    <div class="d-none d-xl-flex justify-content-between gap-3 mb-4">
        <div class="card flex-grow-1">
            <div class="card-body">
                <h5 class="card-title">Total Lessons</h5>
                <p class="card-text h2">{{ total_lessons }}</p>
            </div>
        </div>
        <div class="card flex-grow-1">
            <div class="card-body">
                <h5 class="card-title">Total Hours</h5>
                <p class="card-text h2">{{ total_hours|floatformat:1 }}</p>
            </div>
        </div>
        <div class="card flex-grow-1">
            <div class="card-body">
                <h5 class="card-title">Total Income</h5>
                <p class="card-text h2">£{{ total_income|floatformat:2 }}</p>
            </div>
        </div>
        <div class="card flex-grow-1">
            <div class="card-body">
                <h5 class="card-title">Avg Price/Hour</h5>
                <p class="card-text h2">£{{ avg_price_per_hour|floatformat:2 }}</p>
            </div>
        </div>
        <div class="card flex-grow-1">
            <div class="card-body">
                <h5 class="card-title">Avg Price/Lesson</h5>
                <p class="card-text h2">£{{ avg_price_per_lesson|floatformat:2 }}</p>
            </div>
        </div>
    </div>

    <!-- Second row of average lesson statistics -->
    <div class="d-none d-xl-flex justify-content-between gap-3 mb-4">
        <div class="card flex-grow-1">
            <div class="card-body">
                <div class="text-center">
                    <div class="h6 mb-2">Avg Lessons/Week</div>
                    <div class="h5">{{ avg_lessons_per_week_alltime|floatformat:1 }}</div>
                    <small class="text-muted">(All-time)</small>
                </div>
            </div>
        </div>
        <div class="card flex-grow-1">
            <div class="card-body">
                <div class="text-center">
                    <div class="h6 mb-2">Avg Lessons/Month</div>
                    <div class="h5">{{ avg_lessons_per_month_alltime|floatformat:1 }}</div>
                    <small class="text-muted">(All-time)</small>
                </div>
            </div>
        </div>
        <div class="card flex-grow-1">
            <div class="card-body">
                <div class="text-center">
                    <div class="h6 mb-2">Avg Lessons/Quarter</div>
                    <div class="h5">{{ avg_lessons_per_quarter_alltime|floatformat:1 }}</div>
                    <small class="text-muted">(All-time)</small>
                </div>
            </div>
        </div>
        <div class="card flex-grow-1">
            <div class="card-body">
                <div class="text-center">
                    <div class="h6 mb-2">Avg Hours/Week</div>
                    <div class="h5">{{ avg_hours_per_week_alltime|floatformat:1 }}</div>
                    <small class="text-muted">(All-time)</small>
                </div>
            </div>
        </div>
        <div class="card flex-grow-1">
            <div class="card-body">
                <div class="text-center">
                    <div class="h6 mb-2">Avg Hours/Month</div>
                    <div class="h5">{{ avg_hours_per_month_alltime|floatformat:1 }}</div>
                    <small class="text-muted">(All-time)</small>
                </div>
            </div>
        </div>
        <div class="card flex-grow-1">
            <div class="card-body">
                <div class="text-center">
                    <div class="h6 mb-2">Avg Hours/Quarter</div>
                    <div class="h5">{{ avg_hours_per_quarter_alltime|floatformat:1 }}</div>
                    <small class="text-muted">(All-time)</small>
                </div>
            </div>
        </div>
    </div>

    <!-- Third row of current period lesson statistics -->
    <div class="d-none d-xl-flex justify-content-between gap-3 mb-4">
        <div class="card flex-grow-1">
            <div class="card-body">
                <div class="text-center">
                    <div class="h6 mb-2">This Week Lessons</div>
                    <div class="h5">{{ current_week_lessons_count }}</div>
                    <small class="text-muted">(Current)</small>
                </div>
            </div>
        </div>
        <div class="card flex-grow-1">
            <div class="card-body">
                <div class="text-center">
                    <div class="h6 mb-2">This Month Lessons</div>
                    <div class="h5">{{ current_month_lessons_count }}</div>
                    <small class="text-muted">(Current)</small>
                </div>
            </div>
        </div>
        <div class="card flex-grow-1">
            <div class="card-body">
                <div class="text-center">
                    <div class="h6 mb-2">This Quarter Lessons</div>
                    <div class="h5">{{ current_quarter_lessons_count }}</div>
                    <small class="text-muted">(Current)</small>
                </div>
            </div>
        </div>
        <div class="card flex-grow-1">
            <div class="card-body">
                <div class="text-center">
                    <div class="h6 mb-2">This Week Hours</div>
                    <div class="h5">{{ current_week_hours|floatformat:1 }}</div>
                    <small class="text-muted">(Current)</small>
                </div>
            </div>
        </div>
        <div class="card flex-grow-1">
            <div class="card-body">
                <div class="text-center">
                    <div class="h6 mb-2">This Month Hours</div>
                    <div class="h5">{{ current_month_hours|floatformat:1 }}</div>
                    <small class="text-muted">(Current)</small>
                </div>
            </div>
        </div>
        <div class="card flex-grow-1">
            <div class="card-body">
                <div class="text-center">
                    <div class="h6 mb-2">This Quarter Hours</div>
                    <div class="h5">{{ current_quarter_hours|floatformat:1 }}</div>
                    <small class="text-muted">(Current)</small>
                </div>
            </div>
        </div>
    </div>

    <div class="mb-3">
        <a href="{% url 'lesson_create' %}" class="btn btn-primary w-100 h-100 d-flex align-items-center justify-content-center py-2">
            <div class="text-center">
                <i class="fas fa-chalkboard-teacher fa-2x mb-2"></i>
                <div>Add New Lesson</div>
            </div>
        </a>
    </div>
    <div class="mb-3">
        <input type="text" id="lessonFilter" class="form-control w-100" placeholder="Filter by student name...">
    </div>

    <!-- Wrap table in a div that respects container width -->
    <div style="min-width: auto; overflow-x: auto;">
        <table class="table table-striped">
            <thead>
                <tr>
                    <th style="width: 15%">Date</th>
                    <th style="width: 10%">Day</th>
                    <th style="width: 18%">Student</th>
                    <th style="width: 8%">Hours</th>
                    <th style="width: 10%">Price/Hour</th>
                    <th style="width: 10%">Amount</th>
                    <th style="width: 10%">Test</th>
                    <th style="width: 19%">Actions</th>
                </tr>
            </thead>
            <tbody>
                {% for lesson in lessons %}
                <tr>
                    <td>{{ lesson.date|date:"M d, Y" }}</td>
                    <td>{{ lesson.day_of_week }}</td>
                    <td>
                        <a href="{% url 'student_lessons' student_name=lesson.student_name %}" class="text-decoration-none">
                            {{ lesson.student_name }}
                        </a>
                    </td>
                    <td>{{ lesson.lesson_hours }}</td>
                    <td>£{{ lesson.price_per_hour|floatformat:2 }}</td>
                    <td>£{{ lesson.calculated_amount|floatformat:2 }}</td>
                    <td>
                        {% if lesson.test_status == 'passed' %}
                            <span class="badge bg-success">Passed</span>
                        {% elif lesson.test_status == 'failed' %}
                            <span class="badge bg-danger">Failed</span>
                        {% else %}
                            <span class="text-muted">-</span>
                        {% endif %}
                    </td>
                    <td>
                        <div class="btn-group" role="group">
                            <a href="{% url 'lesson_update' lesson.pk %}" class="btn btn-sm btn-warning">Edit</a>
                            <a href="{% url 'lesson_delete' lesson.pk %}" class="btn btn-sm btn-danger">Delete</a>
                            <button type="button" class="btn btn-sm btn-info" data-bs-toggle="modal" data-bs-target="#notesModal{{ lesson.pk }}">
                                Notes
                            </button>
                        </div>
                    </td>
                </tr>
                <!-- Notes Modal for each lesson -->
                <div class="modal fade" id="notesModal{{ lesson.pk }}" tabindex="-1" aria-labelledby="notesModalLabel{{ lesson.pk }}" aria-hidden="true">
                    <div class="modal-dialog">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h5 class="modal-title" id="notesModalLabel{{ lesson.pk }}">
                                    Notes for {{ lesson.student_name }} - {{ lesson.date|date:"M d, Y" }}
                                </h5>
                                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                            </div>
                            <div class="modal-body">
                                {% if lesson.notes %}
                                    <p class="mb-0">{{ lesson.notes|linebreaks }}</p>
                                {% else %}
                                    <p class="text-muted mb-0">No notes available.</p>
                                {% endif %}
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                            </div>
                        </div>
                    </div>
                </div>
                {% empty %}
                <tr>
                    <td colspan="8" class="text-center">No lessons found.</td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>
</div>
{% endblock %}

{% block javascript %}
{{ block.super }}
<script>
// Enable all tooltips
document.addEventListener('DOMContentLoaded', function() {
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function(tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
});

document.getElementById('lessonFilter').addEventListener('input', function() {
    const filterValue = this.value.toLowerCase();
    const tableRows = document.querySelectorAll('table tbody tr');
    
    tableRows.forEach(row => {
        const studentName = row.querySelector('td:nth-child(3)').textContent.toLowerCase();
        row.style.display = studentName.includes(filterValue) ? '' : 'none';
    });
});
</script>
{% endblock %}
